/**
 * Ocean Soul Sparkles - Mobile Menu Page
 * Full menu page for mobile "More" navigation section
 */

import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import AdminLayout from '../../components/admin/AdminLayout';
import { useAuth } from '../../hooks/useAuth';
import styles from '../../styles/admin/mobile/MobileMenu.module.css';

interface MenuItem {
  id: string;
  label: string;
  icon: string;
  href: string;
  description: string;
  roles: string[];
  category: string;
}

const MENU_ITEMS: MenuItem[] = [
  // Core Features
  {
    id: 'services',
    label: 'Services',
    icon: '✨',
    href: '/admin/services',
    description: 'Manage service offerings and pricing',
    roles: ['DEV', 'Admin'],
    category: 'Core'
  },
  {
    id: 'products',
    label: 'Products',
    icon: '🛍️',
    href: '/admin/products',
    description: 'Product catalog and inventory',
    roles: ['DEV', 'Admin'],
    category: 'Core'
  },
  {
    id: 'inventory',
    label: 'Inventory',
    icon: '📦',
    href: '/admin/inventory',
    description: 'Stock management and suppliers',
    roles: ['DEV', 'Admin'],
    category: 'Core'
  },
  
  // Staff & Artists
  {
    id: 'staff',
    label: 'Staff Management',
    icon: '👨‍💼',
    href: '/admin/staff',
    description: 'Staff onboarding and management',
    roles: ['DEV', 'Admin'],
    category: 'Staff'
  },
  {
    id: 'artists',
    label: 'Artists',
    icon: '🎨',
    href: '/admin/artists',
    description: 'Artist profiles and portfolios',
    roles: ['DEV', 'Admin'],
    category: 'Staff'
  },
  {
    id: 'staff-training',
    label: 'Training',
    icon: '📚',
    href: '/admin/staff/training',
    description: 'Staff training and certification',
    roles: ['DEV', 'Admin'],
    category: 'Staff'
  },
  {
    id: 'staff-performance',
    label: 'Performance',
    icon: '📈',
    href: '/admin/staff/performance',
    description: 'Performance metrics and analytics',
    roles: ['DEV', 'Admin'],
    category: 'Staff'
  },
  
  // Communications
  {
    id: 'communications',
    label: 'Communications',
    icon: '📧',
    href: '/admin/communications',
    description: 'Email templates and messaging',
    roles: ['DEV', 'Admin'],
    category: 'Communications'
  },
  {
    id: 'sms',
    label: 'SMS Notifications',
    icon: '📱',
    href: '/admin/sms',
    description: 'SMS notification management',
    roles: ['DEV', 'Admin'],
    category: 'Communications'
  },
  
  // Analytics & Reports
  {
    id: 'reports',
    label: 'Reports & Analytics',
    icon: '📊',
    href: '/admin/reports',
    description: 'Business analytics and reports',
    roles: ['DEV', 'Admin'],
    category: 'Analytics'
  },
  {
    id: 'receipts',
    label: 'Receipt Templates',
    icon: '🧾',
    href: '/admin/receipts',
    description: 'Customize receipt templates',
    roles: ['DEV', 'Admin'],
    category: 'Analytics'
  },
  
  // System
  {
    id: 'settings',
    label: 'Settings',
    icon: '⚙️',
    href: '/admin/settings',
    description: 'System configuration and preferences',
    roles: ['DEV', 'Admin'],
    category: 'System'
  },
  {
    id: 'help',
    label: 'Help & Support',
    icon: '❓',
    href: '/admin/help',
    description: 'Documentation and support',
    roles: ['DEV', 'Admin', 'Artist', 'Braider'],
    category: 'System'
  }
];

export default function MobileMenu() {
  const router = useRouter();
  const { user } = useAuth();

  const hasAccess = (roles: string[]) => {
    return user && roles.includes(user.role);
  };

  const isActive = (href: string) => {
    return router.pathname === href || router.pathname.startsWith(href + '/');
  };

  const filteredItems = MENU_ITEMS.filter(item => hasAccess(item.roles));
  
  // Group items by category
  const groupedItems = filteredItems.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, MenuItem[]>);

  const categories = [
    { key: 'Core', label: 'Core Features', icon: '🏢' },
    { key: 'Staff', label: 'Staff & Artists', icon: '👥' },
    { key: 'Communications', label: 'Communications', icon: '💬' },
    { key: 'Analytics', label: 'Analytics & Reports', icon: '📊' },
    { key: 'System', label: 'System', icon: '⚙️' }
  ];

  return (
    <>
      <Head>
        <title>Menu - Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Admin dashboard menu and navigation" />
      </Head>

      <AdminLayout>
        <div className={styles.mobileMenu}>
          <div className={styles.header}>
            <h1>Admin Menu</h1>
            <p>Access all admin features and tools</p>
          </div>

          <div className={styles.menuContent}>
            {categories.map(category => {
              const items = groupedItems[category.key];
              if (!items || items.length === 0) return null;

              return (
                <div key={category.key} className={styles.menuSection}>
                  <div className={styles.sectionHeader}>
                    <span className={styles.sectionIcon}>{category.icon}</span>
                    <h2 className={styles.sectionTitle}>{category.label}</h2>
                  </div>

                  <div className={styles.menuGrid}>
                    {items.map(item => (
                      <Link
                        key={item.id}
                        href={item.href}
                        className={`${styles.menuItem} ${isActive(item.href) ? styles.active : ''}`}
                      >
                        <div className={styles.itemIcon}>{item.icon}</div>
                        <div className={styles.itemContent}>
                          <h3 className={styles.itemLabel}>{item.label}</h3>
                          <p className={styles.itemDescription}>{item.description}</p>
                        </div>
                        <div className={styles.itemArrow}>→</div>
                      </Link>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Quick Actions */}
          <div className={styles.quickActions}>
            <h2>Quick Actions</h2>
            <div className={styles.actionGrid}>
              <Link href="/admin/bookings/new" className={styles.actionButton}>
                <span className={styles.actionIcon}>➕</span>
                <span>New Booking</span>
              </Link>
              <Link href="/admin/customers/new" className={styles.actionButton}>
                <span className={styles.actionIcon}>👤</span>
                <span>Add Customer</span>
              </Link>
              <Link href="/admin/pos" className={styles.actionButton}>
                <span className={styles.actionIcon}>💳</span>
                <span>Open POS</span>
              </Link>
              <Link href="/admin/reports" className={styles.actionButton}>
                <span className={styles.actionIcon}>📊</span>
                <span>View Reports</span>
              </Link>
            </div>
          </div>
        </div>
      </AdminLayout>
    </>
  );
}
