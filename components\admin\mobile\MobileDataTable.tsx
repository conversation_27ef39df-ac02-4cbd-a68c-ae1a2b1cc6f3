/**
 * Ocean Soul Sparkles - Mobile Data Table Component
 * Transforms desktop tables into mobile-friendly card layouts
 */

import React, { useState } from 'react';
import styles from '../../../styles/admin/mobile/MobileDataTable.module.css';

interface Column {
  key: string;
  label: string;
  render?: (value: any, row: any) => React.ReactNode;
  sortable?: boolean;
  primary?: boolean; // Primary columns shown prominently
  secondary?: boolean; // Secondary columns shown in collapsed view
}

interface Action {
  label: string;
  icon: string;
  onClick: (row: any) => void;
  variant?: 'primary' | 'secondary' | 'danger';
}

interface MobileDataTableProps {
  data: any[];
  columns: Column[];
  actions?: Action[];
  loading?: boolean;
  emptyMessage?: string;
  searchable?: boolean;
  sortable?: boolean;
  onRowClick?: (row: any) => void;
}

export default function MobileDataTable({
  data,
  columns,
  actions = [],
  loading = false,
  emptyMessage = 'No data available',
  searchable = false,
  sortable = false,
  onRowClick
}: MobileDataTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // Filter data based on search term
  const filteredData = searchable ? data.filter(row => {
    return columns.some(column => {
      const value = row[column.key];
      return value && value.toString().toLowerCase().includes(searchTerm.toLowerCase());
    });
  }) : data;

  // Sort data
  const sortedData = sortable && sortColumn ? [...filteredData].sort((a, b) => {
    const aValue = a[sortColumn];
    const bValue = b[sortColumn];
    
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  }) : filteredData;

  const handleSort = (columnKey: string) => {
    if (!sortable) return;
    
    if (sortColumn === columnKey) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(columnKey);
      setSortDirection('asc');
    }
  };

  const toggleRowExpansion = (rowId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(rowId)) {
      newExpanded.delete(rowId);
    } else {
      newExpanded.add(rowId);
    }
    setExpandedRows(newExpanded);
  };

  const primaryColumns = columns.filter(col => col.primary);
  const secondaryColumns = columns.filter(col => col.secondary);
  const otherColumns = columns.filter(col => !col.primary && !col.secondary);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading data...</p>
      </div>
    );
  }

  return (
    <div className={styles.mobileTable}>
      {/* Search Bar */}
      {searchable && (
        <div className={styles.searchContainer}>
          <div className={styles.searchInput}>
            <span className={styles.searchIcon}>🔍</span>
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      )}

      {/* Sort Controls */}
      {sortable && (
        <div className={styles.sortControls}>
          <span className={styles.sortLabel}>Sort by:</span>
          <div className={styles.sortButtons}>
            {columns.filter(col => col.sortable).map(column => (
              <button
                key={column.key}
                onClick={() => handleSort(column.key)}
                className={`${styles.sortButton} ${sortColumn === column.key ? styles.active : ''}`}
              >
                {column.label}
                {sortColumn === column.key && (
                  <span className={styles.sortDirection}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Data Cards */}
      <div className={styles.cardContainer}>
        {sortedData.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>📄</div>
            <p>{emptyMessage}</p>
          </div>
        ) : (
          sortedData.map((row, index) => {
            const rowId = row.id || index.toString();
            const isExpanded = expandedRows.has(rowId);
            const hasSecondaryData = secondaryColumns.length > 0 || otherColumns.length > 0;

            return (
              <div
                key={rowId}
                className={`${styles.dataCard} ${onRowClick ? styles.clickable : ''}`}
                onClick={() => onRowClick && onRowClick(row)}
              >
                {/* Primary Data */}
                <div className={styles.primaryData}>
                  {primaryColumns.map(column => (
                    <div key={column.key} className={styles.primaryField}>
                      <span className={styles.fieldLabel}>{column.label}:</span>
                      <span className={styles.fieldValue}>
                        {column.render ? column.render(row[column.key], row) : row[column.key]}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Secondary Data (Collapsible) */}
                {hasSecondaryData && (
                  <>
                    {isExpanded && (
                      <div className={styles.secondaryData}>
                        {secondaryColumns.map(column => (
                          <div key={column.key} className={styles.secondaryField}>
                            <span className={styles.fieldLabel}>{column.label}:</span>
                            <span className={styles.fieldValue}>
                              {column.render ? column.render(row[column.key], row) : row[column.key]}
                            </span>
                          </div>
                        ))}
                        {otherColumns.map(column => (
                          <div key={column.key} className={styles.secondaryField}>
                            <span className={styles.fieldLabel}>{column.label}:</span>
                            <span className={styles.fieldValue}>
                              {column.render ? column.render(row[column.key], row) : row[column.key]}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </>
                )}

                {/* Actions and Expand Button */}
                <div className={styles.cardActions}>
                  {actions.map((action, actionIndex) => (
                    <button
                      key={actionIndex}
                      onClick={(e) => {
                        e.stopPropagation();
                        action.onClick(row);
                      }}
                      className={`${styles.actionButton} ${styles[action.variant || 'primary']}`}
                    >
                      <span className={styles.actionIcon}>{action.icon}</span>
                      <span className={styles.actionLabel}>{action.label}</span>
                    </button>
                  ))}

                  {hasSecondaryData && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleRowExpansion(rowId);
                      }}
                      className={styles.expandButton}
                    >
                      {isExpanded ? '▼ Less' : '▶ More'}
                    </button>
                  )}
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Results Count */}
      {sortedData.length > 0 && (
        <div className={styles.resultsCount}>
          Showing {sortedData.length} of {data.length} items
        </div>
      )}
    </div>
  );
}
