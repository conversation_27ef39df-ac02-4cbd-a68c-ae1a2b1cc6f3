/* Ocean Soul Sparkles - Responsive Table Styles */

.responsiveTable {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.searchContainer {
  background: var(--admin-bg-primary);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: var(--admin-shadow-sm);
  border: 1px solid var(--admin-border-light);
}

.searchInput {
  position: relative;
  display: flex;
  align-items: center;
}

.searchIcon {
  position: absolute;
  left: 1rem;
  font-size: 1rem;
  color: var(--admin-gray);
  z-index: 1;
}

.searchInput input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--admin-border-light);
  border-radius: 6px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.searchInput input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
}

.tableContainer {
  background: var(--admin-bg-primary);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--admin-shadow-sm);
  border: 1px solid var(--admin-border-light);
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.table thead {
  background: var(--admin-bg-secondary);
  border-bottom: 2px solid var(--admin-border-light);
}

.table th {
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  color: var(--admin-dark);
  border-bottom: 1px solid var(--admin-border-light);
  position: relative;
}

.table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s ease;
}

.table th.sortable:hover {
  background: var(--admin-hover);
}

.table th.sorted {
  background: rgba(55, 136, 216, 0.1);
  color: var(--admin-primary);
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
}

.sortIndicator {
  font-size: 0.8rem;
  color: var(--admin-gray);
  transition: color 0.3s ease;
}

.table th.sorted .sortIndicator {
  color: var(--admin-primary);
}

.actionsHeader {
  width: 120px;
  text-align: center;
}

.tableRow {
  transition: background-color 0.3s ease;
  border-bottom: 1px solid var(--admin-border-light);
}

.tableRow:hover {
  background: var(--admin-hover);
}

.tableRow.clickable {
  cursor: pointer;
}

.tableRow:last-child {
  border-bottom: none;
}

.tableCell {
  padding: 0.75rem;
  vertical-align: middle;
  color: var(--admin-dark);
  border-bottom: 1px solid var(--admin-border-light);
}

.actionsCell {
  padding: 0.75rem;
  text-align: center;
  border-bottom: 1px solid var(--admin-border-light);
}

.actionButtons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.4rem 0.6rem;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;
}

.actionButton.primary {
  background: var(--admin-primary);
  color: white;
}

.actionButton.primary:hover {
  background: var(--admin-primary-dark);
  transform: translateY(-1px);
}

.actionButton.secondary {
  background: var(--admin-bg-secondary);
  color: var(--admin-dark);
  border: 1px solid var(--admin-border-light);
}

.actionButton.secondary:hover {
  background: var(--admin-hover);
  border-color: var(--admin-border);
}

.actionButton.danger {
  background: var(--admin-danger);
  color: white;
}

.actionButton.danger:hover {
  background: #c53030;
  transform: translateY(-1px);
}

.actionIcon {
  font-size: 0.9rem;
}

.actionLabel {
  font-weight: 500;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: var(--admin-gray);
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState p {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
}

.resultsCount {
  text-align: center;
  font-size: 0.85rem;
  color: var(--admin-gray);
  padding: 1rem;
  background: var(--admin-bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--admin-border-light);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: var(--admin-gray);
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--admin-border-light);
  border-top: 3px solid var(--admin-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive behavior */
@media (max-width: 768px) {
  .responsiveTable {
    display: none; /* Hide desktop table on mobile */
  }
}

/* Desktop-specific optimizations */
@media (min-width: 769px) {
  .table {
    font-size: 0.95rem;
  }

  .table th {
    padding: 1.25rem 1rem;
  }

  .tableCell {
    padding: 1rem;
  }

  .actionsCell {
    padding: 1rem;
  }

  .actionButton {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }

  .actionButtons {
    gap: 0.75rem;
  }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
  .table {
    font-size: 1rem;
  }

  .actionButton .actionLabel {
    display: inline; /* Show action labels on large screens */
  }
}

/* Small desktop screens */
@media (max-width: 1024px) and (min-width: 769px) {
  .actionButton .actionLabel {
    display: none; /* Hide action labels on smaller desktop screens */
  }

  .actionButton {
    padding: 0.4rem;
    min-width: 32px;
    justify-content: center;
  }
}
