import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useAuth } from '@/hooks/useAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import PurchaseOrderForm from '@/components/admin/PurchaseOrderForm';

/**
 * Create New Purchase Order Page
 * 
 * This page provides a form interface for creating new purchase orders
 * with supplier selection and item management.
 */
export default function NewPurchaseOrderPage() {
  const { user, loading: authLoading } = useAuth();

  // Show loading state while authenticating
  if (authLoading) {
    return (
      <AdminLayout>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <div style={{
            width: '32px',
            height: '32px',
            border: '3px solid #e2e8f0',
            borderTop: '3px solid #667eea',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}></div>
          <p style={{ color: '#64748b' }}>Authenticating...</p>
        </div>
      </AdminLayout>
    );
  }

  // Show error state if authentication failed
  if (!user) {
    return (
      <AdminLayout>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <h2 style={{ color: '#ef4444' }}>Authentication Required</h2>
          <p style={{ color: '#64748b' }}>Please log in to create purchase orders.</p>
        </div>
      </AdminLayout>
    );
  }

  // Check user permissions
  if (!['DEV', 'Admin'].includes(user.role)) {
    return (
      <AdminLayout>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <h2 style={{ color: '#ef4444' }}>Access Denied</h2>
          <p style={{ color: '#64748b' }}>You do not have permission to create purchase orders.</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Create Purchase Order | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Create a new purchase order for supplier deliveries" />
      </Head>

      <PurchaseOrderForm />

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </AdminLayout>
  );
}
