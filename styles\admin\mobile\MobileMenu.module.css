/* Ocean Soul Sparkles - Mobile Menu Page Styles */

.mobileMenu {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
  padding-bottom: 100px; /* Account for bottom navigation */
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 2rem 1.5rem;
  text-align: center;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  margin: 0;
  color: #718096;
  font-size: 1rem;
}

.menuContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.menuSection {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.sectionHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.sectionIcon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 10px;
  color: white;
}

.sectionTitle {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
}

.menuGrid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.menuItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  text-decoration: none;
  color: #2d3748;
  transition: all 0.3s ease;
}

.menuItem:hover {
  background: #f1f5f9;
  border-color: #cbd5e0;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.menuItem.active {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  color: #667eea;
}

.itemIcon {
  font-size: 1.5rem;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px;
  color: white;
  flex-shrink: 0;
}

.menuItem.active .itemIcon {
  background: linear-gradient(135deg, #764ba2, #667eea);
}

.itemContent {
  flex: 1;
  min-width: 0;
}

.itemLabel {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: inherit;
}

.itemDescription {
  margin: 0;
  font-size: 0.85rem;
  color: #718096;
  line-height: 1.3;
}

.itemArrow {
  font-size: 1.2rem;
  color: #a0aec0;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.menuItem:hover .itemArrow {
  color: #667eea;
  transform: translateX(3px);
}

.quickActions {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  margin-top: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.quickActions h2 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
}

.actionGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.actionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  text-align: center;
}

.actionButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.actionIcon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.actionButton span:last-child {
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.2;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .mobileMenu {
    padding: 0.75rem;
  }

  .header {
    padding: 1.5rem 1rem;
  }

  .header h1 {
    font-size: 1.5rem;
  }

  .menuSection {
    padding: 1rem;
  }

  .sectionHeader {
    margin-bottom: 1rem;
  }

  .sectionIcon {
    width: 36px;
    height: 36px;
    font-size: 1.3rem;
  }

  .sectionTitle {
    font-size: 1.1rem;
  }

  .menuItem {
    padding: 0.75rem;
  }

  .itemIcon {
    width: 40px;
    height: 40px;
    font-size: 1.3rem;
  }

  .itemLabel {
    font-size: 0.9rem;
  }

  .itemDescription {
    font-size: 0.8rem;
  }

  .actionGrid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .actionButton {
    padding: 1.25rem 1rem;
  }

  .actionIcon {
    width: 36px;
    height: 36px;
    font-size: 1.3rem;
  }
}

/* Hide on desktop - this is a mobile-only page */
@media (min-width: 769px) {
  .mobileMenu {
    display: none;
  }
}
