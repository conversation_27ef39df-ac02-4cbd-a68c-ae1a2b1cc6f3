import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useAuth } from '@/hooks/useAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import SupplierManagement from '@/components/admin/SupplierManagement';

/**
 * Suppliers Management Page
 * 
 * This page provides a comprehensive interface for managing suppliers,
 * including contact information, payment terms, and business relationships.
 */
export default function SuppliersPage() {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [suppliers, setSuppliers] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!authLoading && user) {
      fetchSuppliers();
    }
  }, [user, authLoading]);

  const fetchSuppliers = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('/api/admin/suppliers', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error('You do not have permission to access suppliers.');
        }
        throw new Error(`Failed to fetch suppliers: ${response.status}`);
      }

      const data = await response.json();
      setSuppliers(data.suppliers || []);
      
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Show loading state while authenticating
  if (authLoading) {
    return (
      <AdminLayout>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <div style={{
            width: '32px',
            height: '32px',
            border: '3px solid #e2e8f0',
            borderTop: '3px solid #667eea',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}></div>
          <p style={{ color: '#64748b' }}>Authenticating...</p>
        </div>
      </AdminLayout>
    );
  }

  // Show error state if authentication failed
  if (!user) {
    return (
      <AdminLayout>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <h2 style={{ color: '#ef4444' }}>Authentication Required</h2>
          <p style={{ color: '#64748b' }}>Please log in to access the suppliers page.</p>
        </div>
      </AdminLayout>
    );
  }

  // Check user permissions
  if (!['DEV', 'Admin'].includes(user.role)) {
    return (
      <AdminLayout>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <h2 style={{ color: '#ef4444' }}>Access Denied</h2>
          <p style={{ color: '#64748b' }}>You do not have permission to access supplier management.</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Supplier Management | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage suppliers and vendor relationships" />
      </Head>

      {error ? (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          <h2 style={{ color: '#ef4444' }}>Error Loading Suppliers</h2>
          <p style={{ color: '#64748b' }}>{error}</p>
          <button 
            onClick={fetchSuppliers}
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '8px',
              fontWeight: '600',
              cursor: 'pointer'
            }}
          >
            Try Again
          </button>
        </div>
      ) : (
        <SupplierManagement 
          initialSuppliers={suppliers}
        />
      )}

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </AdminLayout>
  );
}
