const { supabaseAdmin } = require('../supabase-admin');
const smsService = require('../sms/sms-service');
const emailService = require('../email/email-service');

/**
 * Inventory Alerts System
 * 
 * Handles checking inventory levels and creating alerts
 * for low stock, out of stock, and overstock situations
 */

/**
 * Check all inventory items for low stock conditions
 * Creates alerts and sends notifications as needed
 */
async function checkLowStockAlerts() {
  const requestId = `alert-check-${Date.now()}`;
  
  try {
    console.log(`[${requestId}] Starting low stock alert check`);

    // Get all active inventory items with stock levels
    const { data: inventoryItems, error: inventoryError } = await supabaseAdmin
      .from('inventory')
      .select('id, name, sku, quantity_on_hand, min_stock_level, reorder_point, supplier_id')
      .eq('is_active', true);

    if (inventoryError) {
      console.error(`[${requestId}] Error fetching inventory:`, inventoryError);
      throw inventoryError;
    }

    if (!inventoryItems || inventoryItems.length === 0) {
      console.log(`[${requestId}] No inventory items found`);
      return { alertsCreated: 0, notificationsSent: 0 };
    }

    let alertsCreated = 0;
    let notificationsSent = 0;

    for (const item of inventoryItems) {
      const threshold = Math.max(item.min_stock_level || 0, item.reorder_point || 0);
      const currentStock = item.quantity_on_hand || 0;

      // Determine alert type
      let alertType = null;
      if (currentStock === 0) {
        alertType = 'out_of_stock';
      } else if (currentStock <= threshold) {
        alertType = 'low_stock';
      }

      if (alertType) {
        // Check if alert already exists and is active
        const { data: existingAlert } = await supabaseAdmin
          .from('inventory_alerts')
          .select('id')
          .eq('inventory_id', item.id)
          .eq('alert_type', alertType)
          .eq('is_active', true)
          .eq('is_resolved', false)
          .single();

        if (!existingAlert) {
          // Create new alert
          const { data: newAlert, error: alertError } = await supabaseAdmin
            .from('inventory_alerts')
            .insert([{
              inventory_id: item.id,
              alert_type: alertType,
              threshold_value: threshold,
              current_value: currentStock,
              is_active: true,
              is_resolved: false
            }])
            .select()
            .single();

          if (alertError) {
            console.error(`[${requestId}] Error creating alert for ${item.name}:`, alertError);
            continue;
          }

          alertsCreated++;
          console.log(`[${requestId}] Created ${alertType} alert for ${item.name} (${currentStock}/${threshold})`);

          // Send notifications
          try {
            await sendInventoryAlertNotifications(item, alertType, currentStock, threshold, requestId);
            notificationsSent++;
          } catch (notificationError) {
            console.error(`[${requestId}] Error sending notifications for ${item.name}:`, notificationError);
          }
        }
      }
    }

    console.log(`[${requestId}] Alert check completed:`, {
      itemsChecked: inventoryItems.length,
      alertsCreated,
      notificationsSent
    });

    return { alertsCreated, notificationsSent };

  } catch (error) {
    console.error(`[${requestId}] Error in checkLowStockAlerts:`, error);
    throw error;
  }
}

/**
 * Send notifications for inventory alerts
 */
async function sendInventoryAlertNotifications(item, alertType, currentStock, threshold, requestId) {
  try {
    // Get notification settings
    const { data: settings } = await supabaseAdmin
      .from('system_settings')
      .select('key, value')
      .in('key', ['inventory_alerts_enabled', 'inventory_alert_email', 'inventory_alert_sms']);

    const settingsMap = {};
    settings?.forEach(setting => {
      settingsMap[setting.key] = setting.value;
    });

    const alertsEnabled = settingsMap.inventory_alerts_enabled === 'true';
    if (!alertsEnabled) {
      console.log(`[${requestId}] Inventory alerts disabled in settings`);
      return;
    }

    const alertTitle = alertType === 'out_of_stock' ? 'Out of Stock Alert' : 'Low Stock Alert';
    const alertMessage = `${alertTitle}: ${item.name} ${item.sku ? `(${item.sku})` : ''} - Current stock: ${currentStock}, Threshold: ${threshold}`;

    // Send email notification
    const alertEmail = settingsMap.inventory_alert_email;
    if (alertEmail) {
      const emailTemplate = {
        subject: `${alertTitle} - ${item.name}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
              <h1 style="margin: 0; font-size: 24px;">${alertTitle}</h1>
            </div>
            <div style="background: white; padding: 20px; border: 1px solid #e2e8f0; border-radius: 0 0 8px 8px;">
              <h2 style="color: #1e293b; margin-top: 0;">${item.name}</h2>
              ${item.sku ? `<p><strong>SKU:</strong> ${item.sku}</p>` : ''}
              <div style="background: #fee2e2; border: 1px solid #fca5a5; border-radius: 6px; padding: 15px; margin: 15px 0;">
                <p style="margin: 0; color: #991b1b;">
                  <strong>Current Stock:</strong> ${currentStock} units<br>
                  <strong>Threshold:</strong> ${threshold} units
                </p>
              </div>
              <p>This item needs to be restocked to maintain adequate inventory levels.</p>
              <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
                <p style="color: #64748b; font-size: 14px; margin: 0;">
                  Ocean Soul Sparkles Inventory Management System<br>
                  Generated at ${new Date().toLocaleString('en-AU')}
                </p>
              </div>
            </div>
          </div>
        `,
        text: alertMessage
      };

      await emailService.sendEmail(alertEmail, emailTemplate.subject, emailTemplate.html, emailTemplate.text);
      console.log(`[${requestId}] Email notification sent for ${item.name}`);
    }

    // Send SMS notification
    const alertSMS = settingsMap.inventory_alert_sms;
    if (alertSMS) {
      await smsService.sendSMS({ to: alertSMS, message: alertMessage, type: 'inventory_alert' });
      console.log(`[${requestId}] SMS notification sent for ${item.name}`);
    }

  } catch (error) {
    console.error(`[${requestId}] Error sending notifications:`, error);
    throw error;
  }
}

/**
 * Get active inventory alerts
 */
async function getActiveInventoryAlerts() {
  try {
    const { data: alerts, error } = await supabaseAdmin
      .from('inventory_alerts')
      .select(`
        id,
        alert_type,
        threshold_value,
        current_value,
        created_at,
        inventory (
          id,
          name,
          sku,
          quantity_on_hand,
          min_stock_level
        )
      `)
      .eq('is_active', true)
      .eq('is_resolved', false)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return alerts || [];
  } catch (error) {
    console.error('Error fetching inventory alerts:', error);
    throw error;
  }
}

/**
 * Resolve an inventory alert
 */
async function resolveInventoryAlert(alertId, resolvedBy) {
  try {
    const { data: resolvedAlert, error } = await supabaseAdmin
      .from('inventory_alerts')
      .update({
        is_resolved: true,
        resolved_at: new Date().toISOString(),
        resolved_by: resolvedBy
      })
      .eq('id', alertId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return resolvedAlert;
  } catch (error) {
    console.error('Error resolving inventory alert:', error);
    throw error;
  }
}

/**
 * Update inventory stock and check for alert resolution
 */
async function updateInventoryStock(inventoryId, newQuantity) {
  try {
    // Update inventory quantity
    const { data: updatedItem, error: updateError } = await supabaseAdmin
      .from('inventory')
      .update({
        quantity_on_hand: newQuantity,
        updated_at: new Date().toISOString()
      })
      .eq('id', inventoryId)
      .select('id, name, quantity_on_hand, min_stock_level, reorder_point')
      .single();

    if (updateError) {
      throw updateError;
    }

    // Check if any alerts should be resolved
    const threshold = Math.max(updatedItem.min_stock_level || 0, updatedItem.reorder_point || 0);
    
    if (newQuantity > threshold) {
      // Resolve any active low stock or out of stock alerts
      await supabaseAdmin
        .from('inventory_alerts')
        .update({
          is_resolved: true,
          resolved_at: new Date().toISOString()
        })
        .eq('inventory_id', inventoryId)
        .eq('is_active', true)
        .eq('is_resolved', false)
        .in('alert_type', ['low_stock', 'out_of_stock']);

      console.log(`Resolved alerts for ${updatedItem.name} - stock updated to ${newQuantity}`);
    }

    return updatedItem;
  } catch (error) {
    console.error('Error updating inventory stock:', error);
    throw error;
  }
}

module.exports = {
  checkLowStockAlerts,
  getActiveInventoryAlerts,
  resolveInventoryAlert,
  updateInventoryStock
};
