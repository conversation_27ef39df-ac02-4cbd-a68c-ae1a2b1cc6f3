import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface ScheduleOverride {
  artist_id: string;
  override_date: string;
  override_type: 'time_off' | 'custom_hours' | 'unavailable' | 'special_availability';
  start_time?: string;
  end_time?: string;
  reason?: string;
  notes?: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const requestId = uuidv4();
  
  try {
    // Authentication check
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Valid authentication token required',
        requestId
      });
    }

    if (req.method === 'GET') {
      const { 
        artist_id, 
        override_type, 
        status, 
        start_date, 
        end_date,
        limit = 50, 
        offset = 0 
      } = req.query;

      // Get artist availability and overrides
      let availabilityQuery = supabase
        .from('artist_availability')
        .select(`
          id,
          artist_id,
          day_of_week,
          start_time,
          end_time,
          break_start_time,
          break_end_time,
          is_available,
          created_at,
          artist_profiles!inner(
            id,
            name,
            email,
            is_active
          )
        `)
        .order('day_of_week', { ascending: true });

      let overridesQuery = supabase
        .from('artist_schedule_overrides')
        .select(`
          id,
          artist_id,
          override_date,
          override_type,
          start_time,
          end_time,
          reason,
          notes,
          status,
          approved_by,
          approved_at,
          created_by,
          created_at,
          updated_at,
          artist_profiles!inner(
            id,
            name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          ),
          creator:admin_users!created_by(
            id,
            first_name,
            last_name,
            email
          )
        `)
        .order('override_date', { ascending: false });

      // Apply filters
      if (artist_id) {
        availabilityQuery = availabilityQuery.eq('artist_id', artist_id);
        overridesQuery = overridesQuery.eq('artist_id', artist_id);
      }
      if (override_type) {
        overridesQuery = overridesQuery.eq('override_type', override_type);
      }
      if (status) {
        overridesQuery = overridesQuery.eq('status', status);
      }
      if (start_date) {
        overridesQuery = overridesQuery.gte('override_date', start_date);
      }
      if (end_date) {
        overridesQuery = overridesQuery.lte('override_date', end_date);
      }

      // Apply pagination to overrides
      overridesQuery = overridesQuery.range(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string) - 1);

      const [
        { data: availability, error: availabilityError },
        { data: overrides, error: overridesError }
      ] = await Promise.all([
        availabilityQuery,
        overridesQuery
      ]);

      if (availabilityError) {
        console.error('Availability fetch error:', availabilityError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to fetch artist availability',
          requestId
        });
      }

      if (overridesError) {
        console.error('Overrides fetch error:', overridesError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to fetch schedule overrides',
          requestId
        });
      }

      // Get total count for pagination
      const { count: totalOverrides } = await supabase
        .from('artist_schedule_overrides')
        .select('*', { count: 'exact', head: true });

      // Get upcoming bookings for context
      const { data: upcomingBookings } = await supabase
        .from('bookings')
        .select(`
          id,
          start_time,
          end_time,
          status,
          services(name),
          customers(first_name, last_name)
        `)
        .gte('start_time', new Date().toISOString())
        .in('status', ['pending', 'confirmed'])
        .order('start_time', { ascending: true })
        .limit(10);

      // Calculate statistics
      const stats = {
        totalOverrides: overrides?.length || 0,
        pendingOverrides: overrides?.filter(o => o.status === 'pending').length || 0,
        approvedOverrides: overrides?.filter(o => o.status === 'approved').length || 0,
        upcomingBookings: upcomingBookings?.length || 0,
        availableDays: availability?.filter(a => a.is_available).length || 0
      };

      return res.status(200).json({
        availability: availability || [],
        overrides: overrides || [],
        upcomingBookings: upcomingBookings || [],
        stats,
        pagination: {
          total: totalOverrides || 0,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: (parseInt(offset as string) + parseInt(limit as string)) < (totalOverrides || 0)
        },
        requestId
      });
    }

    if (req.method === 'POST') {
      const overrideData: ScheduleOverride = req.body;

      // Validate required fields
      if (!overrideData.artist_id || !overrideData.override_date || !overrideData.override_type) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Missing required fields: artist_id, override_date, override_type',
          requestId
        });
      }

      // Verify artist exists
      const { data: artist, error: artistError } = await supabase
        .from('artist_profiles')
        .select('id, name, email')
        .eq('id', overrideData.artist_id)
        .single();

      if (artistError || !artist) {
        return res.status(404).json({
          error: 'Artist not found',
          message: 'The specified artist does not exist',
          requestId
        });
      }

      // Check for existing overrides on the same date
      const { data: existingOverride, error: existingError } = await supabase
        .from('artist_schedule_overrides')
        .select('id, override_type, status')
        .eq('artist_id', overrideData.artist_id)
        .eq('override_date', overrideData.override_date)
        .single();

      if (existingError && existingError.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Existing override check error:', existingError);
      } else if (existingOverride) {
        return res.status(409).json({
          error: 'Schedule conflict',
          message: 'An override already exists for this date',
          existingOverride,
          requestId
        });
      }

      // Check for conflicting bookings
      const { data: conflictingBookings } = await supabase
        .from('bookings')
        .select('id, start_time, end_time, status')
        .eq('artist_id', overrideData.artist_id)
        .gte('start_time', `${overrideData.override_date}T00:00:00Z`)
        .lt('start_time', `${overrideData.override_date}T23:59:59Z`)
        .in('status', ['pending', 'confirmed']);

      if (conflictingBookings && conflictingBookings.length > 0) {
        return res.status(409).json({
          error: 'Booking conflict',
          message: 'There are existing bookings on this date',
          conflictingBookings,
          requestId
        });
      }

      const newOverride = {
        id: uuidv4(),
        artist_id: overrideData.artist_id,
        override_date: overrideData.override_date,
        override_type: overrideData.override_type,
        start_time: overrideData.start_time || null,
        end_time: overrideData.end_time || null,
        reason: overrideData.reason || null,
        notes: overrideData.notes || null,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: createdOverride, error: createError } = await supabase
        .from('artist_schedule_overrides')
        .insert([newOverride])
        .select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          )
        `)
        .single();

      if (createError) {
        console.error('Schedule override creation error:', createError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to create schedule override',
          requestId
        });
      }

      return res.status(201).json({
        override: createdOverride,
        message: 'Schedule override created successfully',
        requestId
      });
    }

    if (req.method === 'PUT') {
      const { override_id } = req.query;
      const updateData = req.body;

      if (!override_id) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Override ID is required',
          requestId
        });
      }

      // Verify override exists
      const { data: existingOverride, error: fetchError } = await supabase
        .from('artist_schedule_overrides')
        .select('*')
        .eq('id', override_id)
        .single();

      if (fetchError || !existingOverride) {
        return res.status(404).json({
          error: 'Override not found',
          message: 'The specified schedule override does not exist',
          requestId
        });
      }

      // Handle approval/denial
      if (updateData.status && ['approved', 'denied'].includes(updateData.status)) {
        updateData.approved_at = new Date().toISOString();
        // Note: approved_by should be set from the authenticated user
      }

      const updatedData = {
        ...updateData,
        updated_at: new Date().toISOString()
      };

      const { data: updatedOverride, error: updateError } = await supabase
        .from('artist_schedule_overrides')
        .update(updatedData)
        .eq('id', override_id)
        .select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          ),
          approver:admin_users!approved_by(
            id,
            first_name,
            last_name,
            email
          )
        `)
        .single();

      if (updateError) {
        console.error('Schedule override update error:', updateError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to update schedule override',
          requestId
        });
      }

      return res.status(200).json({
        override: updatedOverride,
        message: 'Schedule override updated successfully',
        requestId
      });
    }

    if (req.method === 'DELETE') {
      const { override_id } = req.query;

      if (!override_id) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Override ID is required',
          requestId
        });
      }

      // Verify override exists
      const { data: existingOverride, error: fetchError } = await supabase
        .from('artist_schedule_overrides')
        .select('id, artist_id, override_date, status')
        .eq('id', override_id)
        .single();

      if (fetchError || !existingOverride) {
        return res.status(404).json({
          error: 'Override not found',
          message: 'The specified schedule override does not exist',
          requestId
        });
      }

      const { error: deleteError } = await supabase
        .from('artist_schedule_overrides')
        .delete()
        .eq('id', override_id);

      if (deleteError) {
        console.error('Schedule override deletion error:', deleteError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to delete schedule override',
          requestId
        });
      }

      return res.status(200).json({
        message: 'Schedule override deleted successfully',
        deletedOverride: existingOverride,
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      message: `HTTP method ${req.method} is not supported`,
      requestId
    });

  } catch (error) {
    console.error('Artist schedule API error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      requestId
    });
  }
}
