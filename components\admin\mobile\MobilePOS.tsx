/**
 * Ocean Soul Sparkles - Mobile POS Interface
 * Touch-optimized point of sale interface for mobile devices
 */

import React, { useState, useEffect } from 'react';
import styles from '../../../styles/admin/mobile/MobilePOS.module.css';

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  type: 'service' | 'product';
  category?: string;
}

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
}

interface MobilePOSProps {
  onTransactionComplete: (transaction: any) => void;
  services: any[];
  products: any[];
  customers: Customer[];
}

export default function MobilePOS({
  onTransactionComplete,
  services = [],
  products = [],
  customers = []
}: MobilePOSProps) {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [activeTab, setActiveTab] = useState<'services' | 'products'>('services');
  const [searchTerm, setSearchTerm] = useState('');
  const [showCustomerSearch, setShowCustomerSearch] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'square'>('card');
  const [cashReceived, setCashReceived] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Calculate totals
  const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const tax = subtotal * 0.1; // 10% GST
  const total = subtotal + tax;

  // Filter items based on search
  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.category?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone?.includes(searchTerm)
  );

  const addToCart = (item: any, type: 'service' | 'product') => {
    const existingItem = cart.find(cartItem => cartItem.id === item.id && cartItem.type === type);
    
    if (existingItem) {
      setCart(cart.map(cartItem =>
        cartItem.id === item.id && cartItem.type === type
          ? { ...cartItem, quantity: cartItem.quantity + 1 }
          : cartItem
      ));
    } else {
      setCart([...cart, {
        id: item.id,
        name: item.name,
        price: item.price || item.base_price,
        quantity: 1,
        type,
        category: item.category
      }]);
    }
  };

  const removeFromCart = (itemId: string, type: 'service' | 'product') => {
    setCart(cart.filter(item => !(item.id === itemId && item.type === type)));
  };

  const updateQuantity = (itemId: string, type: 'service' | 'product', quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId, type);
      return;
    }

    setCart(cart.map(item =>
      item.id === itemId && item.type === type
        ? { ...item, quantity }
        : item
    ));
  };

  const clearCart = () => {
    setCart([]);
    setSelectedCustomer(null);
    setShowPayment(false);
    setCashReceived('');
  };

  const processPayment = async () => {
    if (cart.length === 0) return;

    setIsProcessing(true);

    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      const transaction = {
        id: Date.now().toString(),
        customer: selectedCustomer,
        items: cart,
        subtotal,
        tax,
        total,
        paymentMethod,
        cashReceived: paymentMethod === 'cash' ? parseFloat(cashReceived) : null,
        change: paymentMethod === 'cash' ? parseFloat(cashReceived) - total : 0,
        timestamp: new Date().toISOString()
      };

      onTransactionComplete(transaction);
      clearCart();
    } catch (error) {
      console.error('Payment processing error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const canProcessPayment = () => {
    if (cart.length === 0) return false;
    if (paymentMethod === 'cash') {
      const received = parseFloat(cashReceived);
      return !isNaN(received) && received >= total;
    }
    return true;
  };

  return (
    <div className={styles.mobilePOS}>
      {/* Header */}
      <div className={styles.header}>
        <h1>POS Terminal</h1>
        <div className={styles.headerActions}>
          <button 
            onClick={() => setShowCustomerSearch(true)}
            className={styles.customerButton}
          >
            👤 {selectedCustomer ? selectedCustomer.name : 'Select Customer'}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className={styles.mainContent}>
        {!showPayment ? (
          <>
            {/* Search Bar */}
            <div className={styles.searchContainer}>
              <div className={styles.searchInput}>
                <span className={styles.searchIcon}>🔍</span>
                <input
                  type="text"
                  placeholder="Search services and products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Category Tabs */}
            <div className={styles.categoryTabs}>
              <button
                onClick={() => setActiveTab('services')}
                className={`${styles.tabButton} ${activeTab === 'services' ? styles.active : ''}`}
              >
                ✨ Services ({filteredServices.length})
              </button>
              <button
                onClick={() => setActiveTab('products')}
                className={`${styles.tabButton} ${activeTab === 'products' ? styles.active : ''}`}
              >
                🛍️ Products ({filteredProducts.length})
              </button>
            </div>

            {/* Items Grid */}
            <div className={styles.itemsGrid}>
              {activeTab === 'services' ? (
                filteredServices.map(service => (
                  <div
                    key={service.id}
                    className={styles.itemCard}
                    onClick={() => addToCart(service, 'service')}
                  >
                    <div className={styles.itemIcon}>✨</div>
                    <div className={styles.itemInfo}>
                      <h3>{service.name}</h3>
                      <p className={styles.itemCategory}>{service.category}</p>
                      <p className={styles.itemPrice}>${service.base_price}</p>
                    </div>
                  </div>
                ))
              ) : (
                filteredProducts.map(product => (
                  <div
                    key={product.id}
                    className={styles.itemCard}
                    onClick={() => addToCart(product, 'product')}
                  >
                    <div className={styles.itemIcon}>🛍️</div>
                    <div className={styles.itemInfo}>
                      <h3>{product.name}</h3>
                      <p className={styles.itemCategory}>{product.category}</p>
                      <p className={styles.itemPrice}>${product.price}</p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </>
        ) : (
          /* Payment Screen */
          <div className={styles.paymentScreen}>
            <h2>Payment</h2>
            
            {/* Payment Method Selection */}
            <div className={styles.paymentMethods}>
              <button
                onClick={() => setPaymentMethod('card')}
                className={`${styles.paymentMethod} ${paymentMethod === 'card' ? styles.active : ''}`}
              >
                💳 Card
              </button>
              <button
                onClick={() => setPaymentMethod('cash')}
                className={`${styles.paymentMethod} ${paymentMethod === 'cash' ? styles.active : ''}`}
              >
                💵 Cash
              </button>
              <button
                onClick={() => setPaymentMethod('square')}
                className={`${styles.paymentMethod} ${paymentMethod === 'square' ? styles.active : ''}`}
              >
                📱 Square
              </button>
            </div>

            {/* Cash Input */}
            {paymentMethod === 'cash' && (
              <div className={styles.cashInput}>
                <label>Cash Received:</label>
                <input
                  type="number"
                  step="0.01"
                  value={cashReceived}
                  onChange={(e) => setCashReceived(e.target.value)}
                  placeholder="0.00"
                />
                {parseFloat(cashReceived) >= total && (
                  <p className={styles.change}>
                    Change: ${(parseFloat(cashReceived) - total).toFixed(2)}
                  </p>
                )}
              </div>
            )}

            {/* Payment Summary */}
            <div className={styles.paymentSummary}>
              <div className={styles.summaryRow}>
                <span>Subtotal:</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>
              <div className={styles.summaryRow}>
                <span>Tax (10%):</span>
                <span>${tax.toFixed(2)}</span>
              </div>
              <div className={`${styles.summaryRow} ${styles.total}`}>
                <span>Total:</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div>

            {/* Payment Actions */}
            <div className={styles.paymentActions}>
              <button
                onClick={() => setShowPayment(false)}
                className={styles.backButton}
              >
                ← Back
              </button>
              <button
                onClick={processPayment}
                disabled={!canProcessPayment() || isProcessing}
                className={styles.processButton}
              >
                {isProcessing ? '⏳ Processing...' : '✅ Process Payment'}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Cart Summary */}
      {!showPayment && (
        <div className={styles.cartSummary}>
          <div className={styles.cartHeader}>
            <h3>Cart ({cart.length} items)</h3>
            <button onClick={clearCart} className={styles.clearButton}>
              🗑️ Clear
            </button>
          </div>

          <div className={styles.cartItems}>
            {cart.map((item, index) => (
              <div key={`${item.id}-${item.type}-${index}`} className={styles.cartItem}>
                <div className={styles.cartItemInfo}>
                  <span className={styles.cartItemName}>{item.name}</span>
                  <span className={styles.cartItemPrice}>${item.price}</span>
                </div>
                <div className={styles.cartItemControls}>
                  <button
                    onClick={() => updateQuantity(item.id, item.type, item.quantity - 1)}
                    className={styles.quantityButton}
                  >
                    −
                  </button>
                  <span className={styles.quantity}>{item.quantity}</span>
                  <button
                    onClick={() => updateQuantity(item.id, item.type, item.quantity + 1)}
                    className={styles.quantityButton}
                  >
                    +
                  </button>
                  <button
                    onClick={() => removeFromCart(item.id, item.type)}
                    className={styles.removeButton}
                  >
                    ✕
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className={styles.cartTotal}>
            <div className={styles.totalRow}>
              <span>Total: ${total.toFixed(2)}</span>
            </div>
            <button
              onClick={() => setShowPayment(true)}
              disabled={cart.length === 0}
              className={styles.checkoutButton}
            >
              💳 Checkout
            </button>
          </div>
        </div>
      )}

      {/* Customer Search Modal */}
      {showCustomerSearch && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <div className={styles.modalHeader}>
              <h3>Select Customer</h3>
              <button
                onClick={() => setShowCustomerSearch(false)}
                className={styles.closeButton}
              >
                ✕
              </button>
            </div>
            
            <div className={styles.customerSearch}>
              <input
                type="text"
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className={styles.customerList}>
              <div
                className={styles.customerItem}
                onClick={() => {
                  setSelectedCustomer(null);
                  setShowCustomerSearch(false);
                }}
              >
                <strong>Walk-in Customer</strong>
                <span>No customer account</span>
              </div>
              {filteredCustomers.map(customer => (
                <div
                  key={customer.id}
                  className={styles.customerItem}
                  onClick={() => {
                    setSelectedCustomer(customer);
                    setShowCustomerSearch(false);
                  }}
                >
                  <strong>{customer.name}</strong>
                  <span>{customer.email || customer.phone}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
