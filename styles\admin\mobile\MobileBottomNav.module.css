/* Ocean Soul Sparkles - Mobile Bottom Navigation Styles */

.bottomNav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid var(--admin-border-light);
  z-index: 1000;
  display: none; /* Hidden by default, shown on mobile */
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
}

.navContainer {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0.5rem 0;
  max-width: 100%;
  margin: 0 auto;
}

.navItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.25rem;
  text-decoration: none;
  color: var(--admin-gray);
  transition: all 0.3s ease;
  border-radius: 12px;
  min-width: 60px;
  position: relative;
}

.navItem:hover {
  color: var(--admin-primary);
  background: rgba(55, 136, 216, 0.1);
}

.navItem.active {
  color: var(--admin-primary);
  background: rgba(55, 136, 216, 0.15);
}

.navItem.active::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: var(--admin-primary);
  border-radius: 0 0 3px 3px;
}

.navIcon {
  font-size: 1.2rem;
  margin-bottom: 0.25rem;
  display: block;
}

.navLabel {
  font-size: 0.7rem;
  font-weight: 500;
  text-align: center;
  line-height: 1;
}

/* Show bottom nav on mobile devices */
@media (max-width: 768px) {
  .bottomNav {
    display: block;
  }
  
  /* Add bottom padding to body to account for fixed bottom nav */
  body {
    padding-bottom: 70px;
  }
}

/* Adjust for very small screens */
@media (max-width: 480px) {
  .navContainer {
    padding: 0.4rem 0;
  }
  
  .navItem {
    padding: 0.4rem 0.2rem;
    min-width: 50px;
  }
  
  .navIcon {
    font-size: 1.1rem;
    margin-bottom: 0.2rem;
  }
  
  .navLabel {
    font-size: 0.65rem;
  }
}

/* Hide on desktop */
@media (min-width: 769px) {
  .bottomNav {
    display: none !important;
  }
}
