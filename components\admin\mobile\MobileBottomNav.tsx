/**
 * Ocean Soul Sparkles - Mobile Bottom Navigation
 * App-like bottom navigation for primary admin actions on mobile devices
 */

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '../../../styles/admin/mobile/MobileBottomNav.module.css';

interface MobileBottomNavProps {
  userRole: string;
}

interface NavItem {
  id: string;
  label: string;
  icon: string;
  href: string;
  roles: string[];
}

const BOTTOM_NAV_ITEMS: NavItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: '📊',
    href: '/admin/dashboard',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  },
  {
    id: 'bookings',
    label: 'Bookings',
    icon: '📅',
    href: '/admin/bookings',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  },
  {
    id: 'pos',
    label: 'POS',
    icon: '💳',
    href: '/admin/pos',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  },
  {
    id: 'customers',
    label: 'Customers',
    icon: '👥',
    href: '/admin/customers',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'more',
    label: 'More',
    icon: '⋯',
    href: '/admin/menu',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  }
];

export default function MobileBottomNav({ userRole }: MobileBottomNavProps) {
  const router = useRouter();

  const hasAccess = (roles: string[]) => {
    return roles.includes(userRole);
  };

  const isActive = (href: string) => {
    if (href === '/admin/menu') {
      // Special case for "More" - active if not on main nav items
      const mainPaths = ['/admin/dashboard', '/admin/bookings', '/admin/pos', '/admin/customers'];
      return !mainPaths.some(path => router.pathname.startsWith(path));
    }
    return router.pathname === href || router.pathname.startsWith(href + '/');
  };

  const filteredItems = BOTTOM_NAV_ITEMS.filter(item => hasAccess(item.roles));

  return (
    <nav className={styles.bottomNav}>
      <div className={styles.navContainer}>
        {filteredItems.map((item) => (
          <Link
            key={item.id}
            href={item.href}
            className={`${styles.navItem} ${isActive(item.href) ? styles.active : ''}`}
          >
            <span className={styles.navIcon}>{item.icon}</span>
            <span className={styles.navLabel}>{item.label}</span>
          </Link>
        ))}
      </div>
    </nav>
  );
}
