/**
 * Ocean Soul Sparkles - Mobile Hamburger Menu
 * Full-screen mobile navigation menu with touch-friendly interface
 */

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '../../../styles/admin/mobile/MobileHamburgerMenu.module.css';

interface MenuItem {
  id: string;
  label: string;
  icon: string;
  href: string;
  roles: string[];
  children?: MenuItem[];
}

interface MobileHamburgerMenuProps {
  isOpen: boolean;
  onClose: () => void;
  userRole: string;
  userName: string;
}

const MENU_ITEMS: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: '📊',
    href: '/admin/dashboard',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  },
  {
    id: 'bookings',
    label: 'Bookings',
    icon: '📅',
    href: '/admin/bookings',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  },
  {
    id: 'customers',
    label: 'Customers',
    icon: '👥',
    href: '/admin/customers',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'services',
    label: 'Services',
    icon: '✨',
    href: '/admin/services',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'products',
    label: 'Products',
    icon: '🛍️',
    href: '/admin/products',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'pos',
    label: 'POS Terminal',
    icon: '💳',
    href: '/admin/pos',
    roles: ['DEV', 'Admin', 'Artist', 'Braider']
  },
  {
    id: 'inventory',
    label: 'Inventory',
    icon: '📦',
    href: '/admin/inventory',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'staff',
    label: 'Staff Management',
    icon: '👨‍💼',
    href: '/admin/staff',
    roles: ['DEV', 'Admin'],
    children: [
      {
        id: 'staff-list',
        label: 'Staff List',
        icon: '👥',
        href: '/admin/staff',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'staff-onboarding',
        label: 'Onboarding',
        icon: '🎯',
        href: '/admin/staff/onboarding',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'staff-training',
        label: 'Training',
        icon: '📚',
        href: '/admin/staff/training',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'staff-performance',
        label: 'Performance',
        icon: '📈',
        href: '/admin/staff/performance',
        roles: ['DEV', 'Admin']
      }
    ]
  },
  {
    id: 'artists',
    label: 'Artists',
    icon: '🎨',
    href: '/admin/artists',
    roles: ['DEV', 'Admin'],
    children: [
      {
        id: 'artists-list',
        label: 'Artist Profiles',
        icon: '👩‍🎨',
        href: '/admin/artists',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'artists-portfolio',
        label: 'Portfolio',
        icon: '🖼️',
        href: '/admin/artists/portfolio',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'artists-schedule',
        label: 'Schedule',
        icon: '📅',
        href: '/admin/artists/schedule',
        roles: ['DEV', 'Admin']
      },
      {
        id: 'artists-commissions',
        label: 'Commissions',
        icon: '💰',
        href: '/admin/artists/commissions',
        roles: ['DEV', 'Admin']
      }
    ]
  },
  {
    id: 'communications',
    label: 'Communications',
    icon: '📧',
    href: '/admin/communications',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'reports',
    label: 'Reports & Analytics',
    icon: '📊',
    href: '/admin/reports',
    roles: ['DEV', 'Admin']
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: '⚙️',
    href: '/admin/settings',
    roles: ['DEV', 'Admin']
  }
];

export default function MobileHamburgerMenu({ isOpen, onClose, userRole, userName }: MobileHamburgerMenuProps) {
  const router = useRouter();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  // Close menu when route changes
  useEffect(() => {
    const handleRouteChange = () => {
      onClose();
    };

    router.events.on('routeChangeStart', handleRouteChange);
    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [router.events, onClose]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const hasAccess = (roles: string[]) => {
    return roles.includes(userRole);
  };

  const isActive = (href: string) => {
    return router.pathname === href || router.pathname.startsWith(href + '/');
  };

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const filteredMenuItems = MENU_ITEMS.filter(item => hasAccess(item.roles));

  if (!isOpen) return null;

  return (
    <div className={styles.overlay}>
      <div className={styles.menu}>
        {/* Header */}
        <div className={styles.header}>
          <div className={styles.userInfo}>
            <div className={styles.avatar}>
              {userName.charAt(0).toUpperCase()}
            </div>
            <div className={styles.userDetails}>
              <span className={styles.userName}>{userName}</span>
              <span className={styles.userRole}>{userRole}</span>
            </div>
          </div>
          <button className={styles.closeButton} onClick={onClose}>
            ✕
          </button>
        </div>

        {/* Navigation */}
        <nav className={styles.navigation}>
          <ul className={styles.menuList}>
            {filteredMenuItems.map((item) => (
              <li key={item.id} className={styles.menuItem}>
                <div className={styles.menuItemContent}>
                  <Link 
                    href={item.href}
                    className={`${styles.menuLink} ${isActive(item.href) ? styles.active : ''}`}
                  >
                    <span className={styles.menuIcon}>{item.icon}</span>
                    <span className={styles.menuLabel}>{item.label}</span>
                  </Link>
                  
                  {item.children && (
                    <button
                      className={styles.expandButton}
                      onClick={() => toggleExpanded(item.id)}
                    >
                      {expandedItems.includes(item.id) ? '▼' : '▶'}
                    </button>
                  )}
                </div>

                {/* Submenu */}
                {item.children && expandedItems.includes(item.id) && (
                  <ul className={styles.submenu}>
                    {item.children.filter(child => hasAccess(child.roles)).map((child) => (
                      <li key={child.id} className={styles.submenuItem}>
                        <Link 
                          href={child.href}
                          className={`${styles.submenuLink} ${isActive(child.href) ? styles.active : ''}`}
                        >
                          <span className={styles.submenuIcon}>{child.icon}</span>
                          <span className={styles.submenuLabel}>{child.label}</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </nav>

        {/* Footer */}
        <div className={styles.footer}>
          <div className={styles.footerActions}>
            <Link href="/admin/help" className={styles.footerLink}>
              ❓ Help & Support
            </Link>
            <Link href="/admin/settings" className={styles.footerLink}>
              ⚙️ Settings
            </Link>
          </div>
          <div className={styles.version}>
            Ocean Soul Sparkles v1.0.0
          </div>
        </div>
      </div>
    </div>
  );
}
