/**
 * Ocean Soul Sparkles - Responsive Table Component
 * Automatically switches between desktop table and mobile cards based on screen size
 */

import React, { useState, useEffect } from 'react';
import MobileDataTable from './mobile/MobileDataTable';
import styles from '../../styles/admin/ResponsiveTable.module.css';

interface Column {
  key: string;
  label: string;
  render?: (value: any, row: any) => React.ReactNode;
  sortable?: boolean;
  primary?: boolean; // For mobile: shown prominently
  secondary?: boolean; // For mobile: shown in collapsed view
  width?: string; // For desktop table
}

interface Action {
  label: string;
  icon: string;
  onClick: (row: any) => void;
  variant?: 'primary' | 'secondary' | 'danger';
}

interface ResponsiveTableProps {
  data: any[];
  columns: Column[];
  actions?: Action[];
  loading?: boolean;
  emptyMessage?: string;
  searchable?: boolean;
  sortable?: boolean;
  onRowClick?: (row: any) => void;
  className?: string;
}

export default function ResponsiveTable({
  data,
  columns,
  actions = [],
  loading = false,
  emptyMessage = 'No data available',
  searchable = false,
  sortable = false,
  onRowClick,
  className = ''
}: ResponsiveTableProps) {
  const [isMobile, setIsMobile] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Filter data based on search term
  const filteredData = searchable ? data.filter(row => {
    return columns.some(column => {
      const value = row[column.key];
      return value && value.toString().toLowerCase().includes(searchTerm.toLowerCase());
    });
  }) : data;

  // Sort data
  const sortedData = sortable && sortColumn ? [...filteredData].sort((a, b) => {
    const aValue = a[sortColumn];
    const bValue = b[sortColumn];
    
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  }) : filteredData;

  const handleSort = (columnKey: string) => {
    if (!sortable) return;
    
    if (sortColumn === columnKey) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(columnKey);
      setSortDirection('asc');
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading data...</p>
      </div>
    );
  }

  // Mobile view - use MobileDataTable
  if (isMobile) {
    return (
      <MobileDataTable
        data={data}
        columns={columns}
        actions={actions}
        loading={loading}
        emptyMessage={emptyMessage}
        searchable={searchable}
        sortable={sortable}
        onRowClick={onRowClick}
      />
    );
  }

  // Desktop view - traditional table
  return (
    <div className={`${styles.responsiveTable} ${className}`}>
      {/* Search Bar */}
      {searchable && (
        <div className={styles.searchContainer}>
          <div className={styles.searchInput}>
            <span className={styles.searchIcon}>🔍</span>
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      )}

      {/* Desktop Table */}
      <div className={styles.tableContainer}>
        {sortedData.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>📄</div>
            <p>{emptyMessage}</p>
          </div>
        ) : (
          <table className={styles.table}>
            <thead>
              <tr>
                {columns.map(column => (
                  <th
                    key={column.key}
                    style={{ width: column.width }}
                    className={`${sortable && column.sortable ? styles.sortable : ''} ${
                      sortColumn === column.key ? styles.sorted : ''
                    }`}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className={styles.headerContent}>
                      <span>{column.label}</span>
                      {sortable && column.sortable && (
                        <span className={styles.sortIndicator}>
                          {sortColumn === column.key ? (
                            sortDirection === 'asc' ? '↑' : '↓'
                          ) : '↕'}
                        </span>
                      )}
                    </div>
                  </th>
                ))}
                {actions.length > 0 && (
                  <th className={styles.actionsHeader}>Actions</th>
                )}
              </tr>
            </thead>
            <tbody>
              {sortedData.map((row, index) => (
                <tr
                  key={row.id || index}
                  className={`${styles.tableRow} ${onRowClick ? styles.clickable : ''}`}
                  onClick={() => onRowClick && onRowClick(row)}
                >
                  {columns.map(column => (
                    <td key={column.key} className={styles.tableCell}>
                      {column.render ? column.render(row[column.key], row) : row[column.key]}
                    </td>
                  ))}
                  {actions.length > 0 && (
                    <td className={styles.actionsCell}>
                      <div className={styles.actionButtons}>
                        {actions.map((action, actionIndex) => (
                          <button
                            key={actionIndex}
                            onClick={(e) => {
                              e.stopPropagation();
                              action.onClick(row);
                            }}
                            className={`${styles.actionButton} ${styles[action.variant || 'primary']}`}
                            title={action.label}
                          >
                            <span className={styles.actionIcon}>{action.icon}</span>
                            <span className={styles.actionLabel}>{action.label}</span>
                          </button>
                        ))}
                      </div>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {/* Results Count */}
      {sortedData.length > 0 && (
        <div className={styles.resultsCount}>
          Showing {sortedData.length} of {data.length} items
        </div>
      )}
    </div>
  );
}
