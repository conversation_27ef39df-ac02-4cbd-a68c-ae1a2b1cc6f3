import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { toast } from 'react-toastify';
import styles from '../../styles/admin/PurchaseOrders.module.css';

interface PurchaseOrder {
  id: string;
  po_number: string;
  supplier_id: string;
  status: 'draft' | 'sent' | 'confirmed' | 'received' | 'cancelled';
  order_date: string;
  expected_delivery_date: string | null;
  actual_delivery_date: string | null;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  notes: string | null;
  created_at: string;
  suppliers: {
    id: string;
    name: string;
    contact_person: string | null;
    email: string | null;
    phone: string | null;
  };
  admin_users: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

interface PurchaseOrdersProps {
  initialPurchaseOrders?: PurchaseOrder[];
}

export default function PurchaseOrders({ initialPurchaseOrders = [] }: PurchaseOrdersProps) {
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>(initialPurchaseOrders);
  const [filteredOrders, setFilteredOrders] = useState<PurchaseOrder[]>(initialPurchaseOrders);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('order_date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Filter and sort purchase orders
  useEffect(() => {
    let filtered = [...purchaseOrders];

    // Apply search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(po =>
        po.po_number.toLowerCase().includes(search) ||
        po.suppliers.name.toLowerCase().includes(search) ||
        po.notes?.toLowerCase().includes(search)
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(po => po.status === statusFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof PurchaseOrder];
      let bValue: any = b[sortBy as keyof PurchaseOrder];

      // Handle nested supplier name
      if (sortBy === 'supplier_name') {
        aValue = a.suppliers.name;
        bValue = b.suppliers.name;
      }

      // Handle null values
      if (aValue === null) aValue = '';
      if (bValue === null) bValue = '';

      // Convert to string for comparison
      aValue = String(aValue).toLowerCase();
      bValue = String(bValue).toLowerCase();

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredOrders(filtered);
  }, [purchaseOrders, searchTerm, statusFilter, sortBy, sortOrder]);

  const fetchPurchaseOrders = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/purchase-orders', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch purchase orders');
      }

      const data = await response.json();
      setPurchaseOrders(data.purchaseOrders || []);
    } catch (error) {
      console.error('Error fetching purchase orders:', error);
      toast.error('Failed to load purchase orders');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePurchaseOrder = async (poId: string, poNumber: string) => {
    if (!confirm(`Are you sure you want to delete purchase order "${poNumber}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/purchase-orders/${poId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete purchase order');
      }

      toast.success('Purchase order deleted successfully');
      fetchPurchaseOrders(); // Refresh the list
    } catch (error) {
      console.error('Error deleting purchase order:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete purchase order');
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'draft': return styles.statusDraft;
      case 'sent': return styles.statusSent;
      case 'confirmed': return styles.statusConfirmed;
      case 'received': return styles.statusReceived;
      case 'cancelled': return styles.statusCancelled;
      default: return styles.statusDraft;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU');
  };

  return (
    <div className={styles.purchaseOrders}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h1 className={styles.title}>Purchase Orders</h1>
          <p className={styles.subtitle}>Manage purchase orders and supplier deliveries</p>
        </div>
        <div className={styles.headerActions}>
          <Link href="/admin/purchase-orders/new" className={styles.addBtn}>
            + Create Purchase Order
          </Link>
        </div>
      </div>

      {/* Controls */}
      <div className={styles.controls}>
        <div className={styles.searchSection}>
          <input
            type="text"
            placeholder="Search by PO number, supplier, or notes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={styles.searchInput}
          />
        </div>

        <div className={styles.filters}>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className={styles.filterSelect}
          >
            <option value="all">All Statuses</option>
            <option value="draft">Draft</option>
            <option value="sent">Sent</option>
            <option value="confirmed">Confirmed</option>
            <option value="received">Received</option>
            <option value="cancelled">Cancelled</option>
          </select>

          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order as 'asc' | 'desc');
            }}
            className={styles.sortSelect}
          >
            <option value="order_date-desc">Order Date (Newest)</option>
            <option value="order_date-asc">Order Date (Oldest)</option>
            <option value="po_number-asc">PO Number (A-Z)</option>
            <option value="po_number-desc">PO Number (Z-A)</option>
            <option value="supplier_name-asc">Supplier (A-Z)</option>
            <option value="supplier_name-desc">Supplier (Z-A)</option>
            <option value="total_amount-desc">Amount (High-Low)</option>
            <option value="total_amount-asc">Amount (Low-High)</option>
          </select>
        </div>
      </div>

      {/* Purchase Orders List */}
      <div className={styles.ordersContainer}>
        {loading ? (
          <div className={styles.loading}>
            <div className={styles.loadingSpinner}></div>
            <p>Loading purchase orders...</p>
          </div>
        ) : filteredOrders.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>📋</div>
            <h3>No purchase orders found</h3>
            <p>
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Get started by creating your first purchase order'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <Link href="/admin/purchase-orders/new" className={styles.addFirstBtn}>
                Create First Purchase Order
              </Link>
            )}
          </div>
        ) : (
          <div className={styles.ordersGrid}>
            {filteredOrders.map((po) => (
              <div key={po.id} className={styles.orderCard}>
                <div className={styles.cardHeader}>
                  <div className={styles.orderInfo}>
                    <h3 className={styles.poNumber}>{po.po_number}</h3>
                    <span className={`${styles.statusBadge} ${getStatusBadgeClass(po.status)}`}>
                      {po.status.charAt(0).toUpperCase() + po.status.slice(1)}
                    </span>
                  </div>
                </div>

                <div className={styles.cardBody}>
                  <div className={styles.supplierInfo}>
                    <span className={styles.label}>Supplier:</span>
                    <span className={styles.value}>{po.suppliers.name}</span>
                  </div>
                  
                  <div className={styles.orderDetails}>
                    <div className={styles.detailItem}>
                      <span className={styles.label}>Order Date:</span>
                      <span className={styles.value}>{formatDate(po.order_date)}</span>
                    </div>
                    
                    {po.expected_delivery_date && (
                      <div className={styles.detailItem}>
                        <span className={styles.label}>Expected Delivery:</span>
                        <span className={styles.value}>{formatDate(po.expected_delivery_date)}</span>
                      </div>
                    )}
                    
                    <div className={styles.detailItem}>
                      <span className={styles.label}>Total Amount:</span>
                      <span className={styles.value}>{formatCurrency(po.total_amount)}</span>
                    </div>
                  </div>

                  {po.notes && (
                    <div className={styles.notes}>
                      <span className={styles.label}>Notes:</span>
                      <p className={styles.notesText}>{po.notes}</p>
                    </div>
                  )}
                </div>

                <div className={styles.cardFooter}>
                  <div className={styles.cardActions}>
                    <Link 
                      href={`/admin/purchase-orders/${po.id}`} 
                      className={styles.viewBtn}
                    >
                      View Details
                    </Link>
                    {po.status === 'draft' && (
                      <>
                        <Link 
                          href={`/admin/purchase-orders/${po.id}/edit`} 
                          className={styles.editBtn}
                        >
                          Edit
                        </Link>
                        <button
                          onClick={() => handleDeletePurchaseOrder(po.id, po.po_number)}
                          className={styles.deleteBtn}
                        >
                          Delete
                        </button>
                      </>
                    )}
                    {['confirmed', 'sent'].includes(po.status) && (
                      <Link 
                        href={`/admin/purchase-orders/${po.id}/receive`} 
                        className={styles.receiveBtn}
                      >
                        Receive Items
                      </Link>
                    )}
                  </div>
                  
                  <div className={styles.cardMeta}>
                    <span className={styles.createdBy}>
                      Created by {po.admin_users.first_name} {po.admin_users.last_name}
                    </span>
                    <span className={styles.createdDate}>
                      {formatDate(po.created_at)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
