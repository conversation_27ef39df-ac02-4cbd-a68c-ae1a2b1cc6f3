import { verifyAdminToken } from '../../../lib/auth/admin-auth';
import { supabaseAdmin } from '../../../lib/supabase-admin';

/**
 * Purchase Orders API Endpoint
 * 
 * Handles CRUD operations for purchase order management
 * Supports filtering, searching, and pagination
 */
export default async function handler(req, res) {
  // Generate unique request ID for tracking
  const requestId = `purchase-orders-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    console.log(`[${requestId}] Purchase Orders API request:`, {
      method: req.method,
      query: req.query,
      userAgent: req.headers['user-agent']
    });

    // Verify admin authentication
    const authResult = await verifyAdminToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ 
        error: 'Unauthorized',
        requestId 
      });
    }

    // Check admin permissions
    if (!['DEV', 'Admin'].includes(authResult.user.role)) {
      console.log(`[${requestId}] Insufficient permissions:`, authResult.user.role);
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        requestId 
      });
    }

    if (req.method === 'GET') {
      return await handleGetPurchaseOrders(req, res, requestId);
    }

    if (req.method === 'POST') {
      return await handleCreatePurchaseOrder(req, res, requestId, authResult.user);
    }

    return res.status(405).json({ 
      error: 'Method not allowed',
      requestId 
    });

  } catch (error) {
    console.error(`[${requestId}] Purchase Orders API error:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message,
      requestId 
    });
  }
}

/**
 * Handle GET request - Fetch purchase orders with filtering and search
 */
async function handleGetPurchaseOrders(req, res, requestId) {
  try {
    const { 
      search = '', 
      status = 'all', 
      supplier = 'all',
      page = 1, 
      limit = 50,
      sortBy = 'order_date',
      sortOrder = 'desc'
    } = req.query;

    let query = supabaseAdmin
      .from('purchase_orders')
      .select(`
        *,
        suppliers (
          id,
          name,
          contact_person,
          email,
          phone
        ),
        admin_users!purchase_orders_created_by_fkey (
          id,
          first_name,
          last_name,
          email
        )
      `, { count: 'exact' });

    // Apply search filter
    if (search) {
      query = query.or(`po_number.ilike.%${search}%,notes.ilike.%${search}%`);
    }

    // Apply status filter
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    // Apply supplier filter
    if (supplier !== 'all') {
      query = query.eq('supplier_id', supplier);
    }

    // Apply sorting
    const validSortFields = ['po_number', 'order_date', 'expected_delivery_date', 'total_amount', 'status'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'order_date';
    const order = sortOrder === 'asc' ? true : false;
    query = query.order(sortField, { ascending: order });

    // Apply pagination
    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(100, Math.max(1, parseInt(limit)));
    const offset = (pageNum - 1) * limitNum;
    
    query = query.range(offset, offset + limitNum - 1);

    const { data: purchaseOrders, error, count } = await query;

    if (error) {
      console.error(`[${requestId}] Database error:`, error);
      throw error;
    }

    // Calculate pagination info
    const totalPages = Math.ceil(count / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    console.log(`[${requestId}] Purchase orders fetched successfully:`, {
      count: purchaseOrders?.length || 0,
      total: count,
      page: pageNum,
      totalPages
    });

    return res.status(200).json({
      purchaseOrders: purchaseOrders || [],
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: count,
        totalPages,
        hasNextPage,
        hasPrevPage
      },
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error fetching purchase orders:`, error);
    throw error;
  }
}

/**
 * Handle POST request - Create new purchase order
 */
async function handleCreatePurchaseOrder(req, res, requestId, user) {
  try {
    const {
      supplierId,
      orderDate,
      expectedDeliveryDate,
      notes,
      items = []
    } = req.body;

    // Validate required fields
    if (!supplierId) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Supplier ID is required',
        requestId
      });
    }

    if (!items || items.length === 0) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'At least one item is required',
        requestId
      });
    }

    // Validate supplier exists
    const { data: supplier, error: supplierError } = await supabaseAdmin
      .from('suppliers')
      .select('id, name, is_active')
      .eq('id', supplierId)
      .single();

    if (supplierError || !supplier) {
      return res.status(404).json({
        error: 'Supplier not found',
        requestId
      });
    }

    if (!supplier.is_active) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Cannot create purchase order for inactive supplier',
        requestId
      });
    }

    // Validate items and calculate totals
    let subtotal = 0;
    const validatedItems = [];

    for (const item of items) {
      if (!item.inventoryId || !item.quantity || !item.unitCost) {
        return res.status(400).json({
          error: 'Validation failed',
          message: 'All items must have inventory ID, quantity, and unit cost',
          requestId
        });
      }

      // Validate inventory item exists
      const { data: inventoryItem, error: inventoryError } = await supabaseAdmin
        .from('inventory')
        .select('id, name, is_active')
        .eq('id', item.inventoryId)
        .single();

      if (inventoryError || !inventoryItem) {
        return res.status(404).json({
          error: 'Inventory item not found',
          message: `Inventory item ${item.inventoryId} not found`,
          requestId
        });
      }

      const quantity = parseInt(item.quantity);
      const unitCost = parseFloat(item.unitCost);
      const totalCost = quantity * unitCost;

      validatedItems.push({
        inventory_id: item.inventoryId,
        product_name: inventoryItem.name,
        quantity,
        unit_cost: unitCost,
        total_cost: totalCost
      });

      subtotal += totalCost;
    }

    // Calculate tax (10% GST for Australia)
    const taxAmount = subtotal * 0.1;
    const totalAmount = subtotal + taxAmount;

    // Generate PO number
    const { data: poNumberResult, error: poNumberError } = await supabaseAdmin
      .rpc('generate_po_number');

    if (poNumberError) {
      console.error(`[${requestId}] Error generating PO number:`, poNumberError);
      throw poNumberError;
    }

    const poNumber = poNumberResult;

    // Create purchase order
    const purchaseOrderData = {
      po_number: poNumber,
      supplier_id: supplierId,
      status: 'draft',
      order_date: orderDate || new Date().toISOString().split('T')[0],
      expected_delivery_date: expectedDeliveryDate || null,
      subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount,
      notes: notes?.trim() || null,
      created_by: user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: newPurchaseOrder, error: poError } = await supabaseAdmin
      .from('purchase_orders')
      .insert([purchaseOrderData])
      .select()
      .single();

    if (poError) {
      console.error(`[${requestId}] Database error creating purchase order:`, poError);
      throw poError;
    }

    // Create purchase order items
    const itemsWithPOId = validatedItems.map(item => ({
      ...item,
      purchase_order_id: newPurchaseOrder.id
    }));

    const { data: newItems, error: itemsError } = await supabaseAdmin
      .from('purchase_order_items')
      .insert(itemsWithPOId)
      .select();

    if (itemsError) {
      console.error(`[${requestId}] Database error creating purchase order items:`, itemsError);
      // Rollback purchase order
      await supabaseAdmin
        .from('purchase_orders')
        .delete()
        .eq('id', newPurchaseOrder.id);
      throw itemsError;
    }

    console.log(`[${requestId}] Purchase order created successfully:`, {
      id: newPurchaseOrder.id,
      poNumber: newPurchaseOrder.po_number,
      supplier: supplier.name,
      totalAmount: newPurchaseOrder.total_amount,
      itemCount: newItems.length,
      createdBy: user.email
    });

    return res.status(201).json({
      purchaseOrder: {
        ...newPurchaseOrder,
        items: newItems,
        supplier
      },
      message: 'Purchase order created successfully',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error creating purchase order:`, error);
    throw error;
  }
}
