import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface PortfolioItem {
  id?: string;
  artist_id: string;
  title: string;
  description?: string;
  category: string;
  image_url: string;
  thumbnail_url?: string;
  tags?: string[];
  is_featured?: boolean;
  is_public?: boolean;
  display_order?: number;
  work_date?: string;
  customer_consent?: boolean;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const requestId = uuidv4();
  
  try {
    // Authentication check
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Valid authentication token required',
        requestId
      });
    }

    if (req.method === 'GET') {
      const { artist_id, category, is_featured, is_public, limit = 50, offset = 0 } = req.query;

      let query = supabase
        .from('artist_portfolio_items')
        .select(`
          id,
          artist_id,
          title,
          description,
          category,
          image_url,
          thumbnail_url,
          tags,
          is_featured,
          is_public,
          display_order,
          work_date,
          customer_consent,
          created_at,
          updated_at,
          artist_profiles!inner(
            id,
            name,
            email
          )
        `)
        .order('display_order', { ascending: true })
        .order('created_at', { ascending: false });

      // Apply filters
      if (artist_id) {
        query = query.eq('artist_id', artist_id);
      }
      if (category) {
        query = query.eq('category', category);
      }
      if (is_featured !== undefined) {
        query = query.eq('is_featured', is_featured === 'true');
      }
      if (is_public !== undefined) {
        query = query.eq('is_public', is_public === 'true');
      }

      // Apply pagination
      query = query.range(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string) - 1);

      const { data: portfolioItems, error, count } = await query;

      if (error) {
        console.error('Portfolio fetch error:', error);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to fetch portfolio items',
          requestId
        });
      }

      // Get total count for pagination
      const { count: totalCount } = await supabase
        .from('artist_portfolio_items')
        .select('*', { count: 'exact', head: true });

      return res.status(200).json({
        portfolioItems: portfolioItems || [],
        pagination: {
          total: totalCount || 0,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: (parseInt(offset as string) + parseInt(limit as string)) < (totalCount || 0)
        },
        requestId
      });
    }

    if (req.method === 'POST') {
      const portfolioData: PortfolioItem = req.body;

      // Validate required fields
      if (!portfolioData.artist_id || !portfolioData.title || !portfolioData.category || !portfolioData.image_url) {
        return res.status(400).json({
          error: 'Validation error',
          message: 'Missing required fields: artist_id, title, category, image_url',
          requestId
        });
      }

      // Verify artist exists
      const { data: artist, error: artistError } = await supabase
        .from('artist_profiles')
        .select('id, name')
        .eq('id', portfolioData.artist_id)
        .single();

      if (artistError || !artist) {
        return res.status(404).json({
          error: 'Artist not found',
          message: 'The specified artist does not exist',
          requestId
        });
      }

      // Get next display order if not provided
      let displayOrder = portfolioData.display_order || 0;
      if (!portfolioData.display_order) {
        const { data: maxOrder } = await supabase
          .from('artist_portfolio_items')
          .select('display_order')
          .eq('artist_id', portfolioData.artist_id)
          .order('display_order', { ascending: false })
          .limit(1);

        if (maxOrder && maxOrder.length > 0) {
          displayOrder = (maxOrder[0].display_order || 0) + 1;
        }
      }

      const newPortfolioItem = {
        id: uuidv4(),
        artist_id: portfolioData.artist_id,
        title: portfolioData.title,
        description: portfolioData.description || null,
        category: portfolioData.category,
        image_url: portfolioData.image_url,
        thumbnail_url: portfolioData.thumbnail_url || null,
        tags: portfolioData.tags || [],
        is_featured: portfolioData.is_featured || false,
        is_public: portfolioData.is_public !== false, // Default to true
        display_order: displayOrder,
        work_date: portfolioData.work_date || null,
        customer_consent: portfolioData.customer_consent || false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: createdItem, error: createError } = await supabase
        .from('artist_portfolio_items')
        .insert([newPortfolioItem])
        .select(`
          *,
          artist_profiles!inner(
            id,
            name,
            email
          )
        `)
        .single();

      if (createError) {
        console.error('Portfolio creation error:', createError);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to create portfolio item',
          requestId
        });
      }

      return res.status(201).json({
        portfolioItem: createdItem,
        message: 'Portfolio item created successfully',
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      message: `HTTP method ${req.method} is not supported`,
      requestId
    });

  } catch (error) {
    console.error('Portfolio API error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      requestId
    });
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb', // Allow larger uploads for images
    },
  },
};
