const XLSX = require('xlsx');

/**
 * Generate Excel report from report data
 */
export async function generateExcelReport(reportData, dateRange, rangeName) {
  // Create a new workbook
  const workbook = XLSX.utils.book_new();

  // Add overview worksheet
  addOverviewWorksheet(workbook, reportData.overview, rangeName);
  
  // Add revenue worksheet
  addRevenueWorksheet(workbook, reportData.revenue);
  
  // Add bookings worksheet
  addBookingsWorksheet(workbook, reportData.bookings);
  
  // Add customers worksheet
  addCustomersWorksheet(workbook, reportData.customers);

  // Generate buffer
  const buffer = XLSX.write(workbook, { 
    type: 'buffer', 
    bookType: 'xlsx',
    compression: true 
  });

  return buffer;
}

/**
 * Add overview worksheet
 */
function addOverviewWorksheet(workbook, overview, rangeName) {
  const data = [
    ['Ocean Soul Sparkles - Business Report'],
    [`Report Period: ${formatDateRange(rangeName)}`],
    [`Generated: ${new Date().toLocaleDateString('en-AU')}`],
    [],
    ['Executive Summary'],
    [],
    ['Metric', 'Value', 'Growth'],
    ['Total Revenue', formatCurrency(overview.totalRevenue), `${overview.revenueGrowth >= 0 ? '+' : ''}${overview.revenueGrowth.toFixed(1)}%`],
    ['Total Bookings', overview.totalBookings, `${overview.bookingGrowth >= 0 ? '+' : ''}${overview.bookingGrowth.toFixed(1)}%`],
    ['Total Customers', overview.totalCustomers, '-'],
    ['Average Booking Value', formatCurrency(overview.averageBookingValue), '-']
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(data);
  
  // Set column widths
  worksheet['!cols'] = [
    { width: 25 },
    { width: 20 },
    { width: 15 }
  ];

  // Style the header
  if (worksheet['A1']) {
    worksheet['A1'].s = {
      font: { bold: true, sz: 16, color: { rgb: "FFFFFF" } },
      fill: { fgColor: { rgb: "667EEA" } },
      alignment: { horizontal: "center" }
    };
  }

  // Style the summary header
  if (worksheet['A5']) {
    worksheet['A5'].s = {
      font: { bold: true, sz: 14 },
      fill: { fgColor: { rgb: "F3F4F6" } }
    };
  }

  // Style the table header
  ['A7', 'B7', 'C7'].forEach(cell => {
    if (worksheet[cell]) {
      worksheet[cell].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "667EEA" } },
        alignment: { horizontal: "center" }
      };
    }
  });

  XLSX.utils.book_append_sheet(workbook, worksheet, 'Overview');
}

/**
 * Add revenue worksheet
 */
function addRevenueWorksheet(workbook, revenue) {
  const data = [
    ['Revenue Analysis'],
    [],
    ['Revenue by Service'],
    ['Service', 'Revenue', 'Percentage'],
    ...revenue.byService.map(service => [
      service.service,
      service.amount,
      `${service.percentage.toFixed(1)}%`
    ]),
    [],
    ['Daily Revenue'],
    ['Date', 'Amount'],
    ...revenue.daily.map(day => [
      new Date(day.date).toLocaleDateString('en-AU'),
      day.amount
    ])
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(data);
  
  // Set column widths
  worksheet['!cols'] = [
    { width: 25 },
    { width: 15 },
    { width: 15 }
  ];

  // Style headers
  if (worksheet['A1']) {
    worksheet['A1'].s = {
      font: { bold: true, sz: 14 },
      fill: { fgColor: { rgb: "764BA2" } },
      font: { color: { rgb: "FFFFFF" } }
    };
  }

  if (worksheet['A3']) {
    worksheet['A3'].s = {
      font: { bold: true, sz: 12 },
      fill: { fgColor: { rgb: "F3F4F6" } }
    };
  }

  // Style table headers
  ['A4', 'B4', 'C4'].forEach(cell => {
    if (worksheet[cell]) {
      worksheet[cell].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "764BA2" } },
        alignment: { horizontal: "center" }
      };
    }
  });

  const dailyStartRow = 7 + revenue.byService.length;
  if (worksheet[`A${dailyStartRow}`]) {
    worksheet[`A${dailyStartRow}`].s = {
      font: { bold: true, sz: 12 },
      fill: { fgColor: { rgb: "F3F4F6" } }
    };
  }

  [`A${dailyStartRow + 1}`, `B${dailyStartRow + 1}`].forEach(cell => {
    if (worksheet[cell]) {
      worksheet[cell].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "764BA2" } },
        alignment: { horizontal: "center" }
      };
    }
  });

  XLSX.utils.book_append_sheet(workbook, worksheet, 'Revenue');
}

/**
 * Add bookings worksheet
 */
function addBookingsWorksheet(workbook, bookings) {
  const data = [
    ['Booking Analysis'],
    [],
    ['Booking Status Breakdown'],
    ['Status', 'Count', 'Percentage'],
    ...bookings.statusBreakdown.map(status => [
      status.status,
      status.count,
      `${status.percentage.toFixed(1)}%`
    ]),
    [],
    ['Summary Statistics'],
    ['Metric', 'Value'],
    ['Total Bookings', bookings.statusBreakdown.reduce((sum, status) => sum + status.count, 0)],
    ['Cancellation Rate', `${bookings.cancellationRate.toFixed(1)}%`],
    ['Success Rate', `${(100 - bookings.cancellationRate).toFixed(1)}%`]
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(data);
  
  // Set column widths
  worksheet['!cols'] = [
    { width: 20 },
    { width: 15 },
    { width: 15 }
  ];

  // Style headers
  if (worksheet['A1']) {
    worksheet['A1'].s = {
      font: { bold: true, sz: 14, color: { rgb: "FFFFFF" } },
      fill: { fgColor: { rgb: "22C55E" } }
    };
  }

  if (worksheet['A3']) {
    worksheet['A3'].s = {
      font: { bold: true, sz: 12 },
      fill: { fgColor: { rgb: "F3F4F6" } }
    };
  }

  // Style table headers
  ['A4', 'B4', 'C4'].forEach(cell => {
    if (worksheet[cell]) {
      worksheet[cell].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "22C55E" } },
        alignment: { horizontal: "center" }
      };
    }
  });

  const summaryStartRow = 7 + bookings.statusBreakdown.length;
  if (worksheet[`A${summaryStartRow}`]) {
    worksheet[`A${summaryStartRow}`].s = {
      font: { bold: true, sz: 12 },
      fill: { fgColor: { rgb: "F3F4F6" } }
    };
  }

  [`A${summaryStartRow + 1}`, `B${summaryStartRow + 1}`].forEach(cell => {
    if (worksheet[cell]) {
      worksheet[cell].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "22C55E" } },
        alignment: { horizontal: "center" }
      };
    }
  });

  XLSX.utils.book_append_sheet(workbook, worksheet, 'Bookings');
}

/**
 * Add customers worksheet
 */
function addCustomersWorksheet(workbook, customers) {
  const data = [
    ['Customer Analysis'],
    [],
    ['Customer Metrics'],
    ['Metric', 'Value'],
    ['New Customers', customers.newCustomers],
    ['Returning Customers', customers.returningCustomers],
    ['Customer Lifetime Value', formatCurrency(customers.customerLifetimeValue)],
    ['Retention Rate', `${((customers.returningCustomers / (customers.newCustomers + customers.returningCustomers)) * 100).toFixed(1)}%`],
    [],
    ['Customer Distribution'],
    ['Type', 'Count', 'Percentage'],
    ['New Customers', customers.newCustomers, `${((customers.newCustomers / (customers.newCustomers + customers.returningCustomers)) * 100).toFixed(1)}%`],
    ['Returning Customers', customers.returningCustomers, `${((customers.returningCustomers / (customers.newCustomers + customers.returningCustomers)) * 100).toFixed(1)}%`]
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(data);
  
  // Set column widths
  worksheet['!cols'] = [
    { width: 25 },
    { width: 15 },
    { width: 15 }
  ];

  // Style headers
  if (worksheet['A1']) {
    worksheet['A1'].s = {
      font: { bold: true, sz: 14, color: { rgb: "FFFFFF" } },
      fill: { fgColor: { rgb: "3B82F6" } }
    };
  }

  if (worksheet['A3']) {
    worksheet['A3'].s = {
      font: { bold: true, sz: 12 },
      fill: { fgColor: { rgb: "F3F4F6" } }
    };
  }

  if (worksheet['A10']) {
    worksheet['A10'].s = {
      font: { bold: true, sz: 12 },
      fill: { fgColor: { rgb: "F3F4F6" } }
    };
  }

  // Style table headers
  ['A4', 'B4'].forEach(cell => {
    if (worksheet[cell]) {
      worksheet[cell].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "3B82F6" } },
        alignment: { horizontal: "center" }
      };
    }
  });

  ['A11', 'B11', 'C11'].forEach(cell => {
    if (worksheet[cell]) {
      worksheet[cell].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "3B82F6" } },
        alignment: { horizontal: "center" }
      };
    }
  });

  XLSX.utils.book_append_sheet(workbook, worksheet, 'Customers');
}

/**
 * Format currency for display
 */
function formatCurrency(amount) {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD'
  }).format(amount);
}

/**
 * Format date range for display
 */
function formatDateRange(rangeName) {
  const ranges = {
    'last7days': 'Last 7 Days',
    'last30days': 'Last 30 Days',
    'last90days': 'Last 90 Days',
    'thisyear': 'This Year'
  };
  
  return ranges[rangeName] || rangeName;
}
