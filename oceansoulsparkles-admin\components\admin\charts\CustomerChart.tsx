import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import styles from '../../../styles/admin/Charts.module.css';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface CustomerData {
  newCustomers: number;
  returningCustomers: number;
  customerLifetimeValue: number;
  growth?: Array<{ month: string; customers: number }>;
  demographics?: Array<{ ageGroup: string; count: number }>;
  retention?: Array<{ period: string; rate: number }>;
}

interface CustomerChartProps {
  data: CustomerData;
  dateRange: string;
}

export default function CustomerChart({ data, dateRange }: CustomerChartProps) {
  // Prepare customer type doughnut chart data
  const customerTypeData = {
    labels: ['New Customers', 'Returning Customers'],
    datasets: [
      {
        label: 'Customer Type',
        data: [data.newCustomers, data.returningCustomers],
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',   // New - Green
          'rgba(59, 130, 246, 0.8)',  // Returning - Blue
        ],
        borderColor: [
          'rgb(34, 197, 94)',
          'rgb(59, 130, 246)',
        ],
        borderWidth: 2,
        hoverOffset: 10,
      },
    ],
  };

  // Prepare customer growth line chart data (if available)
  const growthChartData = data.growth ? {
    labels: data.growth.map(item => item.month),
    datasets: [
      {
        label: 'New Customers',
        data: data.growth.map(item => item.customers),
        borderColor: 'rgb(102, 126, 234)',
        backgroundColor: 'rgba(102, 126, 234, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(102, 126, 234)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
      },
    ],
  } : null;

  // Prepare demographics bar chart data (if available)
  const demographicsData = data.demographics ? {
    labels: data.demographics.map(item => item.ageGroup),
    datasets: [
      {
        label: 'Customer Count',
        data: data.demographics.map(item => item.count),
        backgroundColor: [
          'rgba(239, 68, 68, 0.8)',   // 18-25
          'rgba(245, 158, 11, 0.8)',  // 26-35
          'rgba(34, 197, 94, 0.8)',   // 36-45
          'rgba(59, 130, 246, 0.8)',  // 46-55
          'rgba(168, 85, 247, 0.8)',  // 55+
        ],
        borderColor: [
          'rgb(239, 68, 68)',
          'rgb(245, 158, 11)',
          'rgb(34, 197, 94)',
          'rgb(59, 130, 246)',
          'rgb(168, 85, 247)',
        ],
        borderWidth: 2,
        borderRadius: 6,
        borderSkipped: false,
      },
    ],
  } : null;

  // Chart options
  const doughnutChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          font: {
            family: 'Inter, sans-serif',
            size: 12,
          },
          color: '#374151',
          padding: 15,
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      title: {
        display: true,
        text: `Customer Type Distribution - ${dateRange}`,
        font: {
          family: 'Inter, sans-serif',
          size: 16,
          weight: 'bold' as const,
        },
        color: '#1f2937',
        padding: 20,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(102, 126, 234, 0.8)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context: any) {
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((context.parsed / total) * 100).toFixed(1);
            return `${context.label}: ${context.parsed} customers (${percentage}%)`;
          },
        },
      },
    },
  };

  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          font: {
            family: 'Inter, sans-serif',
            size: 12,
          },
          color: '#374151',
        },
      },
      title: {
        display: true,
        text: 'Customer Growth Trend',
        font: {
          family: 'Inter, sans-serif',
          size: 16,
          weight: 'bold' as const,
        },
        color: '#1f2937',
        padding: 20,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(102, 126, 234, 0.8)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: function(context: any) {
            return `New Customers: ${context.parsed.y}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
          color: '#6b7280',
        },
      },
      y: {
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
          color: '#6b7280',
          stepSize: 1,
        },
      },
    },
  };

  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Customer Demographics',
        font: {
          family: 'Inter, sans-serif',
          size: 16,
          weight: 'bold' as const,
        },
        color: '#1f2937',
        padding: 20,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(102, 126, 234, 0.8)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: function(context: any) {
            return `Customers: ${context.parsed.y}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
          color: '#6b7280',
        },
      },
      y: {
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
          color: '#6b7280',
          stepSize: 1,
        },
      },
    },
  };

  return (
    <div className={styles.chartsContainer}>
      {/* Customer Type Distribution */}
      <div className={styles.chartCard}>
        <div className={styles.chartWrapper}>
          <Doughnut data={customerTypeData} options={doughnutChartOptions} />
        </div>
      </div>

      {/* Customer Growth Trend (if data available) */}
      {growthChartData && (
        <div className={styles.chartCard}>
          <div className={styles.chartWrapper}>
            <Line data={growthChartData} options={lineChartOptions} />
          </div>
        </div>
      )}

      {/* Demographics Chart (if data available) */}
      {demographicsData && (
        <div className={styles.chartCard}>
          <div className={styles.chartWrapper}>
            <Bar data={demographicsData} options={barChartOptions} />
          </div>
        </div>
      )}

      {/* Customer Metrics Summary */}
      <div className={styles.summaryTable}>
        <h3>Customer Metrics</h3>
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{data.newCustomers}</div>
            <div className={styles.statLabel}>New Customers</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{data.returningCustomers}</div>
            <div className={styles.statLabel}>Returning Customers</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>${data.customerLifetimeValue.toFixed(2)}</div>
            <div className={styles.statLabel}>Avg. Lifetime Value</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>
              {((data.returningCustomers / (data.newCustomers + data.returningCustomers)) * 100).toFixed(1)}%
            </div>
            <div className={styles.statLabel}>Retention Rate</div>
          </div>
        </div>
      </div>
    </div>
  );
}
