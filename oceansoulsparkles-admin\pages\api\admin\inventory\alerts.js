import { verifyAdminToken } from '../../../../lib/auth/admin-auth';
import { 
  checkLowStockAlerts, 
  getActiveInventoryAlerts, 
  resolveInventoryAlert 
} from '../../../../lib/alerts/inventory-alerts';

/**
 * Inventory Alerts API Endpoint
 * 
 * Handles inventory alert operations including:
 * - GET: Fetch active alerts
 * - POST: Trigger alert check or resolve alerts
 */
export default async function handler(req, res) {
  // Generate unique request ID for tracking
  const requestId = `alerts-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    console.log(`[${requestId}] Inventory alerts API request:`, {
      method: req.method,
      query: req.query,
      userAgent: req.headers['user-agent']
    });

    // Verify admin authentication
    const authResult = await verifyAdminToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ 
        error: 'Unauthorized',
        requestId 
      });
    }

    // Check admin permissions (all admin roles can view alerts)
    if (!['DEV', 'Admin', 'Artist', 'Braider'].includes(authResult.user.role)) {
      console.log(`[${requestId}] Insufficient permissions:`, authResult.user.role);
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        requestId 
      });
    }

    if (req.method === 'GET') {
      return await handleGetAlerts(req, res, requestId);
    }

    if (req.method === 'POST') {
      // Only DEV and Admin can trigger checks or resolve alerts
      if (!['DEV', 'Admin'].includes(authResult.user.role)) {
        return res.status(403).json({ 
          error: 'Insufficient permissions for this operation',
          requestId 
        });
      }
      return await handlePostAlerts(req, res, requestId, authResult.user);
    }

    return res.status(405).json({ 
      error: 'Method not allowed',
      requestId 
    });

  } catch (error) {
    console.error(`[${requestId}] Inventory alerts API error:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message,
      requestId 
    });
  }
}

/**
 * Handle GET request - Fetch active inventory alerts
 */
async function handleGetAlerts(req, res, requestId) {
  try {
    const alerts = await getActiveInventoryAlerts();

    // Transform alerts for frontend consumption
    const transformedAlerts = alerts.map(alert => ({
      id: alert.id,
      alertType: alert.alert_type,
      thresholdValue: alert.threshold_value,
      currentValue: alert.current_value,
      createdAt: alert.created_at,
      severity: alert.alert_type === 'out_of_stock' ? 'critical' : 'warning',
      item: {
        id: alert.inventory?.id,
        name: alert.inventory?.name,
        sku: alert.inventory?.sku,
        currentStock: alert.inventory?.quantity_on_hand,
        minStockLevel: alert.inventory?.min_stock_level
      }
    }));

    // Group alerts by severity
    const criticalAlerts = transformedAlerts.filter(alert => alert.severity === 'critical');
    const warningAlerts = transformedAlerts.filter(alert => alert.severity === 'warning');

    console.log(`[${requestId}] Alerts fetched successfully:`, {
      total: transformedAlerts.length,
      critical: criticalAlerts.length,
      warning: warningAlerts.length
    });

    return res.status(200).json({
      alerts: transformedAlerts,
      summary: {
        total: transformedAlerts.length,
        critical: criticalAlerts.length,
        warning: warningAlerts.length
      },
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error fetching alerts:`, error);
    throw error;
  }
}

/**
 * Handle POST request - Trigger alert check or resolve alerts
 */
async function handlePostAlerts(req, res, requestId, user) {
  try {
    const { action, alertId } = req.body;

    if (action === 'check') {
      // Trigger manual alert check
      console.log(`[${requestId}] Manual alert check triggered by ${user.email}`);
      
      const result = await checkLowStockAlerts();
      
      console.log(`[${requestId}] Alert check completed:`, result);
      
      return res.status(200).json({
        message: 'Alert check completed',
        result,
        requestId
      });
    }

    if (action === 'resolve') {
      // Resolve specific alert
      if (!alertId) {
        return res.status(400).json({
          error: 'Alert ID is required for resolve action',
          requestId
        });
      }

      console.log(`[${requestId}] Resolving alert ${alertId} by ${user.email}`);
      
      const resolvedAlert = await resolveInventoryAlert(alertId, user.id);
      
      console.log(`[${requestId}] Alert resolved successfully:`, {
        alertId: resolvedAlert.id,
        resolvedBy: user.email
      });
      
      return res.status(200).json({
        message: 'Alert resolved successfully',
        alert: resolvedAlert,
        requestId
      });
    }

    if (action === 'resolve_all') {
      // Resolve all active alerts (bulk action)
      console.log(`[${requestId}] Bulk resolving all alerts by ${user.email}`);
      
      const alerts = await getActiveInventoryAlerts();
      const resolvedCount = alerts.length;
      
      // Resolve all alerts
      for (const alert of alerts) {
        await resolveInventoryAlert(alert.id, user.id);
      }
      
      console.log(`[${requestId}] Bulk resolve completed:`, {
        resolvedCount,
        resolvedBy: user.email
      });
      
      return res.status(200).json({
        message: `${resolvedCount} alerts resolved successfully`,
        resolvedCount,
        requestId
      });
    }

    return res.status(400).json({
      error: 'Invalid action. Supported actions: check, resolve, resolve_all',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error handling POST request:`, error);
    throw error;
  }
}

/**
 * Cron endpoint for automated alert checking
 * This can be called by external cron services or scheduled tasks
 */
export async function runScheduledAlertCheck() {
  const requestId = `scheduled-${Date.now()}`;
  
  try {
    console.log(`[${requestId}] Running scheduled alert check`);
    
    const result = await checkLowStockAlerts();
    
    console.log(`[${requestId}] Scheduled alert check completed:`, result);
    
    return result;
  } catch (error) {
    console.error(`[${requestId}] Error in scheduled alert check:`, error);
    throw error;
  }
}
