-- Ocean Soul Sparkles Artist/Staff Features Database Setup
-- Additional tables for Portfolio Management, Advanced Scheduling, and Commission Tracking

-- Ensure we can create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. ARTIST PORTFOLIO ITEMS TABLE
CREATE TABLE IF NOT EXISTS artist_portfolio_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  artist_id UUID REFERENCES artist_profiles(id) ON DELETE CASCADE,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(50) NOT NULL, -- 'face_painting', 'hair_braiding', 'glitter_art', 'body_art', 'special_effects'
  image_url TEXT NOT NULL,
  thumbnail_url TEXT,
  tags TEXT[], -- Array of tags for better categorization
  is_featured BOOLEAN DEFAULT false,
  is_public BOOLEAN DEFAULT true, -- Whether to show in public portfolio
  display_order INTEGER DEFAULT 0,
  work_date DATE, -- When the work was done
  customer_consent BOOLEAN DEFAULT false, -- Customer gave consent to use image
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. ARTIST SCHEDULE OVERRIDES TABLE
CREATE TABLE IF NOT EXISTS artist_schedule_overrides (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  artist_id UUID REFERENCES artist_profiles(id) ON DELETE CASCADE,
  override_date DATE NOT NULL,
  override_type VARCHAR(20) NOT NULL, -- 'time_off', 'custom_hours', 'unavailable', 'special_availability'
  start_time TIME,
  end_time TIME,
  reason VARCHAR(200),
  notes TEXT,
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'approved', 'denied'
  approved_by UUID REFERENCES admin_users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. COMMISSION TRANSACTIONS TABLE
CREATE TABLE IF NOT EXISTS commission_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  artist_id UUID REFERENCES artist_profiles(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  payment_id UUID REFERENCES payments(id) ON DELETE SET NULL,
  service_amount DECIMAL(10,2) NOT NULL, -- Total service amount
  commission_rate DECIMAL(5,2) NOT NULL, -- Commission percentage at time of transaction
  commission_amount DECIMAL(10,2) NOT NULL, -- Calculated commission amount
  tip_amount DECIMAL(10,2) DEFAULT 0.00, -- Tips earned
  total_earnings DECIMAL(10,2) NOT NULL, -- Commission + tips
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'calculated', 'paid', 'disputed'
  payment_method VARCHAR(20), -- 'cash', 'bank_transfer', 'payroll'
  paid_at TIMESTAMP WITH TIME ZONE,
  paid_by UUID REFERENCES admin_users(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. COMMISSION REPORTS TABLE
CREATE TABLE IF NOT EXISTS commission_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  artist_id UUID REFERENCES artist_profiles(id) ON DELETE CASCADE,
  report_period_start DATE NOT NULL,
  report_period_end DATE NOT NULL,
  total_bookings INTEGER DEFAULT 0,
  total_service_amount DECIMAL(12,2) DEFAULT 0.00,
  total_commission_amount DECIMAL(12,2) DEFAULT 0.00,
  total_tips DECIMAL(10,2) DEFAULT 0.00,
  total_earnings DECIMAL(12,2) DEFAULT 0.00,
  average_commission_rate DECIMAL(5,2),
  report_data JSONB, -- Detailed breakdown data
  status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'finalized', 'paid'
  generated_by UUID REFERENCES admin_users(id),
  finalized_by UUID REFERENCES admin_users(id),
  finalized_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. STAFF SCHEDULE REQUESTS TABLE (For time-off and schedule change requests)
CREATE TABLE IF NOT EXISTS staff_schedule_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  staff_id UUID REFERENCES admin_users(id) ON DELETE CASCADE,
  request_type VARCHAR(20) NOT NULL, -- 'time_off', 'schedule_change', 'shift_swap'
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  start_time TIME,
  end_time TIME,
  reason TEXT,
  replacement_staff_id UUID REFERENCES admin_users(id), -- For shift swaps
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'approved', 'denied', 'cancelled'
  priority VARCHAR(10) DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
  approved_by UUID REFERENCES admin_users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  denial_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_artist_portfolio_items_artist_id ON artist_portfolio_items(artist_id);
CREATE INDEX IF NOT EXISTS idx_artist_portfolio_items_category ON artist_portfolio_items(category);
CREATE INDEX IF NOT EXISTS idx_artist_portfolio_items_is_featured ON artist_portfolio_items(is_featured);
CREATE INDEX IF NOT EXISTS idx_artist_portfolio_items_is_public ON artist_portfolio_items(is_public);
CREATE INDEX IF NOT EXISTS idx_artist_portfolio_items_display_order ON artist_portfolio_items(display_order);

CREATE INDEX IF NOT EXISTS idx_artist_schedule_overrides_artist_id ON artist_schedule_overrides(artist_id);
CREATE INDEX IF NOT EXISTS idx_artist_schedule_overrides_date ON artist_schedule_overrides(override_date);
CREATE INDEX IF NOT EXISTS idx_artist_schedule_overrides_type ON artist_schedule_overrides(override_type);
CREATE INDEX IF NOT EXISTS idx_artist_schedule_overrides_status ON artist_schedule_overrides(status);

CREATE INDEX IF NOT EXISTS idx_commission_transactions_artist_id ON commission_transactions(artist_id);
CREATE INDEX IF NOT EXISTS idx_commission_transactions_booking_id ON commission_transactions(booking_id);
CREATE INDEX IF NOT EXISTS idx_commission_transactions_status ON commission_transactions(status);
CREATE INDEX IF NOT EXISTS idx_commission_transactions_created_at ON commission_transactions(created_at);

CREATE INDEX IF NOT EXISTS idx_commission_reports_artist_id ON commission_reports(artist_id);
CREATE INDEX IF NOT EXISTS idx_commission_reports_period ON commission_reports(report_period_start, report_period_end);
CREATE INDEX IF NOT EXISTS idx_commission_reports_status ON commission_reports(status);

CREATE INDEX IF NOT EXISTS idx_staff_schedule_requests_staff_id ON staff_schedule_requests(staff_id);
CREATE INDEX IF NOT EXISTS idx_staff_schedule_requests_dates ON staff_schedule_requests(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_staff_schedule_requests_status ON staff_schedule_requests(status);
CREATE INDEX IF NOT EXISTS idx_staff_schedule_requests_type ON staff_schedule_requests(request_type);

-- Enable Row Level Security
ALTER TABLE artist_portfolio_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE artist_schedule_overrides ENABLE ROW LEVEL SECURITY;
ALTER TABLE commission_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE commission_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_schedule_requests ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies (Allow admin access to everything)
CREATE POLICY IF NOT EXISTS "Admin full access artist_portfolio_items" ON artist_portfolio_items FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access artist_schedule_overrides" ON artist_schedule_overrides FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access commission_transactions" ON commission_transactions FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access commission_reports" ON commission_reports FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access staff_schedule_requests" ON staff_schedule_requests FOR ALL USING (true);

-- Sample data for testing
INSERT INTO artist_portfolio_items (artist_id, title, description, category, image_url, thumbnail_url, tags, is_featured, is_public, display_order, work_date, customer_consent) VALUES
('650e8400-e29b-41d4-a716-446655440001', 'Butterfly Face Paint', 'Beautiful butterfly design with glitter accents', 'face_painting', '/images/portfolio/butterfly-face-paint.jpg', '/images/portfolio/thumbs/butterfly-face-paint.jpg', ARRAY['butterfly', 'glitter', 'colorful'], true, true, 1, '2024-12-01', true),
('650e8400-e29b-41d4-a716-446655440001', 'Rainbow Hair Braids', 'Intricate rainbow-colored hair braiding', 'hair_braiding', '/images/portfolio/rainbow-braids.jpg', '/images/portfolio/thumbs/rainbow-braids.jpg', ARRAY['rainbow', 'braids', 'colorful'], true, true, 2, '2024-11-28', true),
('650e8400-e29b-41d4-a716-446655440002', 'Mermaid Scales', 'Detailed mermaid scale body art', 'body_art', '/images/portfolio/mermaid-scales.jpg', '/images/portfolio/thumbs/mermaid-scales.jpg', ARRAY['mermaid', 'scales', 'blue'], false, true, 3, '2024-11-25', true),
('650e8400-e29b-41d4-a716-446655440003', 'Galaxy Glitter Art', 'Stunning galaxy-themed glitter application', 'glitter_art', '/images/portfolio/galaxy-glitter.jpg', '/images/portfolio/thumbs/galaxy-glitter.jpg', ARRAY['galaxy', 'stars', 'purple'], true, true, 4, '2024-11-20', true)
ON CONFLICT (id) DO NOTHING;

-- Sample schedule overrides
INSERT INTO artist_schedule_overrides (artist_id, override_date, override_type, start_time, end_time, reason, status, approved_by) VALUES
('650e8400-e29b-41d4-a716-446655440001', '2024-12-25', 'time_off', NULL, NULL, 'Christmas Day', 'approved', '650e8400-e29b-41d4-a716-446655440000'),
('650e8400-e29b-41d4-a716-446655440002', '2024-12-31', 'custom_hours', '10:00:00', '15:00:00', 'New Years Eve - Early finish', 'approved', '650e8400-e29b-41d4-a716-446655440000'),
('650e8400-e29b-41d4-a716-446655440003', '2024-12-20', 'time_off', NULL, NULL, 'Personal appointment', 'pending', NULL)
ON CONFLICT (id) DO NOTHING;

-- Sample commission transactions (based on existing bookings)
INSERT INTO commission_transactions (artist_id, booking_id, service_amount, commission_rate, commission_amount, tip_amount, total_earnings, status) VALUES
('650e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 120.00, 30.00, 36.00, 15.00, 51.00, 'calculated'),
('650e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002', 85.00, 30.00, 25.50, 10.00, 35.50, 'calculated'),
('650e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', 95.00, 25.00, 23.75, 8.00, 31.75, 'pending')
ON CONFLICT (id) DO NOTHING;

-- Sample staff schedule requests
INSERT INTO staff_schedule_requests (staff_id, request_type, start_date, end_date, reason, status) VALUES
('650e8400-e29b-41d4-a716-446655440001', 'time_off', '2024-12-23', '2024-12-26', 'Christmas holidays', 'pending'),
('650e8400-e29b-41d4-a716-446655440002', 'schedule_change', '2024-12-15', '2024-12-15', 'Doctor appointment', 'approved'),
('650e8400-e29b-41d4-a716-446655440003', 'time_off', '2024-12-30', '2025-01-02', 'New Year break', 'pending')
ON CONFLICT (id) DO NOTHING;
