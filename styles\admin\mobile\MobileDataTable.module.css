/* Ocean Soul Sparkles - Mobile Data Table Styles */

.mobileTable {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.searchContainer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.searchInput {
  position: relative;
  display: flex;
  align-items: center;
}

.searchIcon {
  position: absolute;
  left: 1rem;
  font-size: 1rem;
  color: #a0aec0;
  z-index: 1;
}

.searchInput input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.searchInput input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
}

.sortControls {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.sortLabel {
  font-size: 0.9rem;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 0.5rem;
  display: block;
}

.sortButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.sortButton {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sortButton:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.sortButton.active {
  background: var(--admin-primary);
  border-color: var(--admin-primary);
  color: white;
}

.sortDirection {
  font-size: 0.8rem;
  margin-left: 0.25rem;
}

.cardContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.dataCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.dataCard.clickable {
  cursor: pointer;
}

.dataCard.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.primaryData {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.primaryField {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.fieldLabel {
  font-size: 0.8rem;
  font-weight: 600;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.fieldValue {
  font-size: 1rem;
  font-weight: 500;
  color: #2d3748;
  word-break: break-word;
}

.secondaryData {
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.secondaryField {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.secondaryField .fieldLabel {
  font-size: 0.85rem;
  color: #4a5568;
  text-transform: none;
  letter-spacing: normal;
  flex-shrink: 0;
}

.secondaryField .fieldValue {
  font-size: 0.9rem;
  text-align: right;
  flex-shrink: 1;
  min-width: 0;
}

.cardActions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.actionButton.primary {
  background: var(--admin-primary);
  color: white;
}

.actionButton.primary:hover {
  background: var(--admin-primary-dark);
  transform: translateY(-1px);
}

.actionButton.secondary {
  background: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.actionButton.secondary:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.actionButton.danger {
  background: var(--admin-danger);
  color: white;
}

.actionButton.danger:hover {
  background: #c53030;
  transform: translateY(-1px);
}

.actionIcon {
  font-size: 0.9rem;
}

.actionLabel {
  font-weight: 500;
}

.expandButton {
  background: none;
  border: none;
  color: var(--admin-primary);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.expandButton:hover {
  background: rgba(55, 136, 216, 0.1);
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: #718096;
}

.emptyIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.resultsCount {
  text-align: center;
  font-size: 0.85rem;
  color: #718096;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: #718096;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid var(--admin-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .dataCard {
    padding: 1rem;
  }

  .primaryField {
    gap: 0.2rem;
  }

  .fieldLabel {
    font-size: 0.75rem;
  }

  .fieldValue {
    font-size: 0.9rem;
  }

  .secondaryField {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .secondaryField .fieldValue {
    text-align: left;
  }

  .cardActions {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .actionButton {
    justify-content: center;
    padding: 0.75rem;
  }

  .sortButtons {
    flex-direction: column;
  }

  .sortButton {
    justify-content: center;
  }
}

/* Hide on desktop - mobile-only component */
@media (min-width: 769px) {
  .mobileTable {
    display: none;
  }
}
