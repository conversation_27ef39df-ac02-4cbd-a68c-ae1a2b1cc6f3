/* Ocean Soul Sparkles - Mobile Hamburger Menu Styles */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: none; /* Hidden by default, shown on mobile */
}

.menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 85%;
  max-width: 320px;
  height: 100vh;
  background: var(--admin-bg-primary);
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.header {
  padding: 1.5rem 1rem 1rem;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-dark));
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
}

.userDetails {
  display: flex;
  flex-direction: column;
}

.userName {
  font-weight: 600;
  font-size: 0.9rem;
  line-height: 1.2;
}

.userRole {
  font-size: 0.75rem;
  opacity: 0.8;
  line-height: 1.2;
}

.closeButton {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
}

.navigation {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
}

.menuList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menuItem {
  margin-bottom: 0.25rem;
}

.menuItemContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
}

.menuLink {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  text-decoration: none;
  color: var(--admin-dark);
  transition: all 0.3s ease;
  flex: 1;
  border-radius: 8px;
  margin-right: 0.5rem;
}

.menuLink:hover {
  color: var(--admin-primary);
  background: rgba(55, 136, 216, 0.1);
  padding-left: 0.5rem;
}

.menuLink.active {
  color: var(--admin-primary);
  background: rgba(55, 136, 216, 0.15);
  font-weight: 600;
  padding-left: 0.5rem;
}

.menuIcon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.menuLabel {
  font-size: 0.9rem;
  font-weight: 500;
}

.expandButton {
  background: none;
  border: none;
  color: var(--admin-gray);
  font-size: 0.8rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expandButton:hover {
  background: rgba(55, 136, 216, 0.1);
  color: var(--admin-primary);
}

.submenu {
  list-style: none;
  padding: 0;
  margin: 0;
  background: var(--admin-bg-secondary);
  border-radius: 8px;
  margin: 0.25rem 1rem 0.5rem 1rem;
  overflow: hidden;
}

.submenuItem {
  border-bottom: 1px solid var(--admin-border-light);
}

.submenuItem:last-child {
  border-bottom: none;
}

.submenuLink {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--admin-gray);
  transition: all 0.3s ease;
  font-size: 0.85rem;
}

.submenuLink:hover {
  color: var(--admin-primary);
  background: rgba(55, 136, 216, 0.1);
}

.submenuLink.active {
  color: var(--admin-primary);
  background: rgba(55, 136, 216, 0.15);
  font-weight: 600;
}

.submenuIcon {
  font-size: 1rem;
  width: 20px;
  text-align: center;
}

.submenuLabel {
  font-weight: 500;
}

.footer {
  padding: 1rem;
  border-top: 1px solid var(--admin-border-light);
  background: var(--admin-bg-secondary);
}

.footerActions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.footerLink {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  text-decoration: none;
  color: var(--admin-gray);
  font-size: 0.85rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.footerLink:hover {
  color: var(--admin-primary);
  background: rgba(55, 136, 216, 0.1);
}

.version {
  text-align: center;
  font-size: 0.75rem;
  color: var(--admin-gray);
  opacity: 0.7;
}

/* Show on mobile devices */
@media (max-width: 768px) {
  .overlay {
    display: block;
  }
}

/* Adjust for very small screens */
@media (max-width: 480px) {
  .menu {
    width: 90%;
    max-width: 280px;
  }
  
  .header {
    padding: 1rem 0.75rem 0.75rem;
  }
  
  .menuItemContent {
    padding: 0 0.75rem;
  }
  
  .submenu {
    margin: 0.25rem 0.75rem 0.5rem 0.75rem;
  }
  
  .footer {
    padding: 0.75rem;
  }
}

/* Hide on desktop */
@media (min-width: 769px) {
  .overlay {
    display: none !important;
  }
}
