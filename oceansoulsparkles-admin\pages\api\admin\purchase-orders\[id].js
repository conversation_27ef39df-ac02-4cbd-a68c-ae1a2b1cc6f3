import { verifyAdminToken } from '../../../../lib/auth/admin-auth';
import { supabaseAdmin } from '../../../../lib/supabase-admin';

/**
 * Individual Purchase Order API Endpoint
 * 
 * Handles CRUD operations for individual purchase orders
 * Supports GET, PUT, and DELETE operations
 */
export default async function handler(req, res) {
  // Generate unique request ID for tracking
  const requestId = `purchase-order-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    const { id } = req.query;

    console.log(`[${requestId}] Individual purchase order API request:`, {
      method: req.method,
      purchaseOrderId: id,
      userAgent: req.headers['user-agent']
    });

    // Validate purchase order ID
    if (!id || typeof id !== 'string') {
      return res.status(400).json({ 
        error: 'Invalid purchase order ID',
        requestId 
      });
    }

    // Verify admin authentication
    const authResult = await verifyAdminToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ 
        error: 'Unauthorized',
        requestId 
      });
    }

    // Check admin permissions
    if (!['DEV', 'Admin'].includes(authResult.user.role)) {
      console.log(`[${requestId}] Insufficient permissions:`, authResult.user.role);
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        requestId 
      });
    }

    if (req.method === 'GET') {
      return await handleGetPurchaseOrder(req, res, requestId, id);
    }

    if (req.method === 'PUT') {
      return await handleUpdatePurchaseOrder(req, res, requestId, id, authResult.user);
    }

    if (req.method === 'DELETE') {
      return await handleDeletePurchaseOrder(req, res, requestId, id, authResult.user);
    }

    return res.status(405).json({ 
      error: 'Method not allowed',
      requestId 
    });

  } catch (error) {
    console.error(`[${requestId}] Individual purchase order API error:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message,
      requestId 
    });
  }
}

/**
 * Handle GET request - Fetch individual purchase order with items
 */
async function handleGetPurchaseOrder(req, res, requestId, purchaseOrderId) {
  try {
    // Fetch purchase order details
    const { data: purchaseOrder, error: poError } = await supabaseAdmin
      .from('purchase_orders')
      .select(`
        *,
        suppliers (
          id,
          name,
          contact_person,
          email,
          phone,
          address,
          payment_terms,
          lead_time_days
        ),
        admin_users!purchase_orders_created_by_fkey (
          id,
          first_name,
          last_name,
          email
        )
      `)
      .eq('id', purchaseOrderId)
      .single();

    if (poError) {
      if (poError.code === 'PGRST116') {
        return res.status(404).json({
          error: 'Purchase order not found',
          requestId
        });
      }
      throw poError;
    }

    // Fetch purchase order items
    const { data: items, error: itemsError } = await supabaseAdmin
      .from('purchase_order_items')
      .select(`
        *,
        inventory (
          id,
          name,
          sku,
          quantity_on_hand,
          min_stock_level
        )
      `)
      .eq('purchase_order_id', purchaseOrderId)
      .order('created_at');

    if (itemsError) {
      console.warn(`[${requestId}] Error fetching purchase order items:`, itemsError);
    }

    console.log(`[${requestId}] Purchase order fetched successfully:`, {
      id: purchaseOrder.id,
      poNumber: purchaseOrder.po_number,
      supplier: purchaseOrder.suppliers?.name,
      itemCount: items?.length || 0
    });

    return res.status(200).json({
      purchaseOrder: {
        ...purchaseOrder,
        items: items || []
      },
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error fetching purchase order:`, error);
    throw error;
  }
}

/**
 * Handle PUT request - Update purchase order
 */
async function handleUpdatePurchaseOrder(req, res, requestId, purchaseOrderId, user) {
  try {
    const {
      status,
      expectedDeliveryDate,
      actualDeliveryDate,
      notes
    } = req.body;

    // Check if purchase order exists
    const { data: existingPO, error: fetchError } = await supabaseAdmin
      .from('purchase_orders')
      .select('id, po_number, status, supplier_id')
      .eq('id', purchaseOrderId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          error: 'Purchase order not found',
          requestId
        });
      }
      throw fetchError;
    }

    // Validate status transition
    const validStatuses = ['draft', 'sent', 'confirmed', 'received', 'cancelled'];
    if (status && !validStatuses.includes(status)) {
      return res.status(400).json({
        error: 'Invalid status',
        message: `Status must be one of: ${validStatuses.join(', ')}`,
        requestId
      });
    }

    // Prevent modification of received or cancelled orders
    if (['received', 'cancelled'].includes(existingPO.status) && status !== existingPO.status) {
      return res.status(400).json({
        error: 'Cannot modify completed purchase order',
        message: 'Purchase orders that are received or cancelled cannot be modified',
        requestId
      });
    }

    // Update purchase order
    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (status) updateData.status = status;
    if (expectedDeliveryDate) updateData.expected_delivery_date = expectedDeliveryDate;
    if (actualDeliveryDate) updateData.actual_delivery_date = actualDeliveryDate;
    if (notes !== undefined) updateData.notes = notes?.trim() || null;

    const { data: updatedPO, error: updateError } = await supabaseAdmin
      .from('purchase_orders')
      .update(updateData)
      .eq('id', purchaseOrderId)
      .select(`
        *,
        suppliers (
          id,
          name,
          contact_person,
          email
        )
      `)
      .single();

    if (updateError) {
      console.error(`[${requestId}] Database error updating purchase order:`, updateError);
      throw updateError;
    }

    console.log(`[${requestId}] Purchase order updated successfully:`, {
      id: updatedPO.id,
      poNumber: updatedPO.po_number,
      status: updatedPO.status,
      updatedBy: user.email
    });

    return res.status(200).json({
      purchaseOrder: updatedPO,
      message: 'Purchase order updated successfully',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error updating purchase order:`, error);
    throw error;
  }
}

/**
 * Handle DELETE request - Delete purchase order (only if draft)
 */
async function handleDeletePurchaseOrder(req, res, requestId, purchaseOrderId, user) {
  try {
    // Check if purchase order exists and get status
    const { data: existingPO, error: fetchError } = await supabaseAdmin
      .from('purchase_orders')
      .select('id, po_number, status')
      .eq('id', purchaseOrderId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          error: 'Purchase order not found',
          requestId
        });
      }
      throw fetchError;
    }

    // Only allow deletion of draft purchase orders
    if (existingPO.status !== 'draft') {
      return res.status(400).json({
        error: 'Cannot delete purchase order',
        message: 'Only draft purchase orders can be deleted',
        requestId
      });
    }

    // Delete purchase order (items will be deleted automatically due to CASCADE)
    const { error: deleteError } = await supabaseAdmin
      .from('purchase_orders')
      .delete()
      .eq('id', purchaseOrderId);

    if (deleteError) {
      console.error(`[${requestId}] Database error deleting purchase order:`, deleteError);
      throw deleteError;
    }

    console.log(`[${requestId}] Purchase order deleted successfully:`, {
      id: purchaseOrderId,
      poNumber: existingPO.po_number,
      deletedBy: user.email
    });

    return res.status(200).json({
      message: 'Purchase order deleted successfully',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error deleting purchase order:`, error);
    throw error;
  }
}
