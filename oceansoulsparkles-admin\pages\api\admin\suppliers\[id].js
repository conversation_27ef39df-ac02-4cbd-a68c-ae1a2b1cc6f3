import { verifyAdminToken } from '../../../../lib/auth/admin-auth';
import { supabaseAdmin } from '../../../../lib/supabase-admin';

/**
 * Individual Supplier API Endpoint
 * 
 * Handles CRUD operations for individual suppliers
 * Supports GET, PUT, and DELETE operations
 */
export default async function handler(req, res) {
  // Generate unique request ID for tracking
  const requestId = `supplier-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    const { id } = req.query;

    console.log(`[${requestId}] Individual supplier API request:`, {
      method: req.method,
      supplierId: id,
      userAgent: req.headers['user-agent']
    });

    // Validate supplier ID
    if (!id || typeof id !== 'string') {
      return res.status(400).json({ 
        error: 'Invalid supplier ID',
        requestId 
      });
    }

    // Verify admin authentication
    const authResult = await verifyAdminToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ 
        error: 'Unauthorized',
        requestId 
      });
    }

    // Check admin permissions
    if (!['DEV', 'Admin'].includes(authResult.user.role)) {
      console.log(`[${requestId}] Insufficient permissions:`, authResult.user.role);
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        requestId 
      });
    }

    if (req.method === 'GET') {
      return await handleGetSupplier(req, res, requestId, id);
    }

    if (req.method === 'PUT') {
      return await handleUpdateSupplier(req, res, requestId, id, authResult.user);
    }

    if (req.method === 'DELETE') {
      return await handleDeleteSupplier(req, res, requestId, id, authResult.user);
    }

    return res.status(405).json({ 
      error: 'Method not allowed',
      requestId 
    });

  } catch (error) {
    console.error(`[${requestId}] Individual supplier API error:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message,
      requestId 
    });
  }
}

/**
 * Handle GET request - Fetch individual supplier with related data
 */
async function handleGetSupplier(req, res, requestId, supplierId) {
  try {
    // Fetch supplier details
    const { data: supplier, error: supplierError } = await supabaseAdmin
      .from('suppliers')
      .select('*')
      .eq('id', supplierId)
      .single();

    if (supplierError) {
      if (supplierError.code === 'PGRST116') {
        return res.status(404).json({
          error: 'Supplier not found',
          requestId
        });
      }
      throw supplierError;
    }

    // Fetch related inventory items
    const { data: inventoryItems, error: inventoryError } = await supabaseAdmin
      .from('inventory')
      .select('id, name, sku, quantity_on_hand, min_stock_level')
      .eq('supplier_id', supplierId)
      .eq('is_active', true)
      .order('name');

    if (inventoryError) {
      console.warn(`[${requestId}] Error fetching inventory items:`, inventoryError);
    }

    // Fetch recent purchase orders
    const { data: purchaseOrders, error: poError } = await supabaseAdmin
      .from('purchase_orders')
      .select('id, po_number, status, order_date, total_amount')
      .eq('supplier_id', supplierId)
      .order('order_date', { ascending: false })
      .limit(10);

    if (poError) {
      console.warn(`[${requestId}] Error fetching purchase orders:`, poError);
    }

    console.log(`[${requestId}] Supplier fetched successfully:`, {
      id: supplier.id,
      name: supplier.name,
      inventoryItems: inventoryItems?.length || 0,
      purchaseOrders: purchaseOrders?.length || 0
    });

    return res.status(200).json({
      supplier,
      inventoryItems: inventoryItems || [],
      purchaseOrders: purchaseOrders || [],
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error fetching supplier:`, error);
    throw error;
  }
}

/**
 * Handle PUT request - Update supplier
 */
async function handleUpdateSupplier(req, res, requestId, supplierId, user) {
  try {
    const {
      name,
      contactPerson,
      email,
      phone,
      address,
      website,
      paymentTerms,
      leadTimeDays,
      minimumOrderAmount,
      isActive,
      notes
    } = req.body;

    // Validate required fields
    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Supplier name is required',
        requestId
      });
    }

    // Check if supplier exists
    const { data: existingSupplier, error: fetchError } = await supabaseAdmin
      .from('suppliers')
      .select('id, name')
      .eq('id', supplierId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          error: 'Supplier not found',
          requestId
        });
      }
      throw fetchError;
    }

    // Check for duplicate name (excluding current supplier)
    const { data: duplicateSupplier } = await supabaseAdmin
      .from('suppliers')
      .select('id')
      .eq('name', name.trim())
      .neq('id', supplierId)
      .single();

    if (duplicateSupplier) {
      return res.status(409).json({
        error: 'Supplier name already exists',
        message: 'Another supplier with this name already exists',
        requestId
      });
    }

    // Validate email format if provided
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Invalid email format',
        requestId
      });
    }

    // Update supplier
    const updateData = {
      name: name.trim(),
      contact_person: contactPerson?.trim() || null,
      email: email?.trim() || null,
      phone: phone?.trim() || null,
      address: address?.trim() || null,
      website: website?.trim() || null,
      payment_terms: paymentTerms?.trim() || 'Net 30',
      lead_time_days: leadTimeDays ? parseInt(leadTimeDays) : 7,
      minimum_order_amount: minimumOrderAmount ? parseFloat(minimumOrderAmount) : 0.00,
      is_active: isActive !== undefined ? Boolean(isActive) : true,
      notes: notes?.trim() || null,
      updated_at: new Date().toISOString()
    };

    const { data: updatedSupplier, error: updateError } = await supabaseAdmin
      .from('suppliers')
      .update(updateData)
      .eq('id', supplierId)
      .select()
      .single();

    if (updateError) {
      console.error(`[${requestId}] Database error updating supplier:`, updateError);
      throw updateError;
    }

    console.log(`[${requestId}] Supplier updated successfully:`, {
      id: updatedSupplier.id,
      name: updatedSupplier.name,
      updatedBy: user.id
    });

    return res.status(200).json({
      supplier: updatedSupplier,
      message: 'Supplier updated successfully',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error updating supplier:`, error);
    throw error;
  }
}

/**
 * Handle DELETE request - Delete supplier (soft delete by setting inactive)
 */
async function handleDeleteSupplier(req, res, requestId, supplierId, user) {
  try {
    // Check if supplier exists
    const { data: existingSupplier, error: fetchError } = await supabaseAdmin
      .from('suppliers')
      .select('id, name')
      .eq('id', supplierId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          error: 'Supplier not found',
          requestId
        });
      }
      throw fetchError;
    }

    // Check if supplier has active purchase orders
    const { data: activePOs, error: poError } = await supabaseAdmin
      .from('purchase_orders')
      .select('id')
      .eq('supplier_id', supplierId)
      .in('status', ['draft', 'sent', 'confirmed'])
      .limit(1);

    if (poError) {
      console.warn(`[${requestId}] Error checking purchase orders:`, poError);
    }

    if (activePOs && activePOs.length > 0) {
      return res.status(409).json({
        error: 'Cannot delete supplier',
        message: 'Supplier has active purchase orders. Please complete or cancel them first.',
        requestId
      });
    }

    // Soft delete by setting inactive
    const { data: deletedSupplier, error: deleteError } = await supabaseAdmin
      .from('suppliers')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', supplierId)
      .select()
      .single();

    if (deleteError) {
      console.error(`[${requestId}] Database error deleting supplier:`, deleteError);
      throw deleteError;
    }

    console.log(`[${requestId}] Supplier deleted successfully:`, {
      id: deletedSupplier.id,
      name: deletedSupplier.name,
      deletedBy: user.id
    });

    return res.status(200).json({
      message: 'Supplier deleted successfully',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error deleting supplier:`, error);
    throw error;
  }
}
